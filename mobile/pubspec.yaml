name: s3g
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ">=3.3.4 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  go_router: ^15.1.0
  dio: ^5.8.0+1
  http_cache_file_store: ^2.0.1
  json_annotation: ^4.9.0
  flutter_bloc: ^9.1.0
  equatable: ^2.0.5
  internet_connection_checker_plus: ^2.4.1
  get_it: ^8.0.3
  injectable: ^2.4.1
  getwidget: ^6.0.0
  flutter_secure_storage: ^10.0.0-beta.4
  flutter_svg: ^2.0.10+1
  fpdart: ^1.1.0
  stream_transform: ^2.1.0
  bloc_concurrency: ^0.3.0
  bloc: ^9.0.0
  meta: ^1.11.0
  flutter_native_splash: ^2.4.0
  flutter_form_builder: ^10.0.1
  form_builder_validators: ^11.1.2
  path_provider: ^2.1.3
  flutter_localizations:
    sdk: flutter
  intl: ^0.18.1
  device_info_plus: ^11.4.0
  objectbox: ^4.3.0
  objectbox_flutter_libs: any
  path: ^1.9.0
  timeago: ^3.6.1
  timeago_flutter: ^3.6.0
  confirm_dialog: ^1.0.3
  flutter_slidable: ^4.0.0
  file_picker: ^10.1.2
  image_picker: ^1.1.2
  dio_cache_interceptor: ^4.0.3
  firebase_messaging: ^15.2.5
  firebase_core: ^3.13.0
  awesome_notifications: ^0.10.1
  open_file: ^3.3.2
  http: ^1.2.1
  modal_bottom_sheet: ^3.0.0
  flutter_widget_from_html: ^0.16.0
  uuid: ^4.5.1

dependency_overrides:
  intl: ^0.19.0
  meta: ^1.12.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  build_runner: ^2.4.9
  json_serializable: ^6.8.0
  flutter_launcher_icons: ^0.14.3
  injectable_generator: ^2.6.1
  objectbox_generator: any

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/different_love.svg
    - assets/empty_re_opql.svg
    - assets/s3g.png

objectbox:
  # Writes objectbox-model.json and objectbox.g.dart to lib/custom (and test/custom).
  output_dir: core/objectbox

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
