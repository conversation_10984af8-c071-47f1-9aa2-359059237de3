# Offline Mode Implementation Plan

## Overview
This plan outlines the implementation of offline support for the S3G mobile application. The implementation follows an offline-first architecture using ObjectBox for local storage and a sync service for data synchronization.

## Architecture Update
After initial analysis, the architecture has been refined to follow clean architecture principles:
- **Single Repository Pattern**: Each feature has ONE repository that handles both online and offline scenarios
- **No Duplicate Blocs/UseCases**: The offline logic is handled at the data layer (repository), not the presentation layer
- **Unified Interface**: The domain layer remains unchanged, maintaining a clean separation of concerns

## Current Status
### ✅ Already Implemented
- **Core Infrastructure**
  - ObjectBox setup (`lib/core/objectbox/`)
  - Sync Service (`lib/src/sync/`)
  - SyncStatus enum (`lib/src/sync/status.dart`)
  - Offline widgets (`lib/common/widgets/offline_demo_widget.dart`, `sync_status_widget.dart`)

- **Features with Offline Support**
  - Followup (companion) - `lib/src/companion/followup/`
  - Instance (manager) - `lib/src/manager/instance/`
  - Companion (manager) - `lib/src/manager/instance_features/companion/`
  - Member (manager) - `lib/src/manager/member/` (caching only, no sync)
  - Questionnaire (manager) - `lib/src/manager/questionnaire/` (caching only, no sync)
  
- **Sync Handlers**
  - `InstanceSyncHandler` - `lib/src/sync/handlers/instance_sync_handler.dart`
  - `CompanionFollowupSyncHandler` - `lib/src/sync/handlers/companion_followup_sync_handler.dart`
  - `CompanionRelapseSyncHandler` - `lib/src/sync/handlers/companion_relapse_sync_handler.dart`
  - `ManagerCompanionSyncHandler` - `lib/src/sync/handlers/manager_companion_sync_handler.dart`

### 🔄 Pending Implementation
1. **Companion Features**
   - Relapse - `lib/src/companion/relapse/`

2. **Manager Instance Features**
   - Companion - `lib/src/manager/instance_features/companion/`
   - Diagnostic - `lib/src/manager/instance_features/diagnostic/`
   - Followup - `lib/src/manager/instance_features/followup/`
   - Relapse - `lib/src/manager/instance_features/relapse/`
   - Treatment - `lib/src/manager/instance_features/treatment/`

## Implementation Tasks

### Phase 1: Relapse Feature (Companion) ✅
Location: `lib/src/companion/relapse/`

#### 1.1 Create Local Data Layer ✅
- [x] Create `lib/src/companion/relapse/data/local/models/relapse_local_model.dart`
  - ObjectBox entity with sync fields
  - Factory methods for conversion
- [x] Create `lib/src/companion/relapse/data/local/relapse_local_datasource.dart`
  - CRUD operations
  - Sync status management

#### 1.2 Update Repository Implementation ✅
- [x] Modify `lib/src/companion/relapse/data/repository/relapse_repository_impl.dart`
  - Add local datasource, connection checker, sync service dependencies
  - Implement offline logic for all methods
  - Handle online/offline scenarios

#### 1.3 Update BLoC States ✅
- [x] Update existing bloc states to include offline status
- [x] Add ConnectionChecker dependency to blocs
- [x] Update success states to include isOffline flag

#### 1.4 Update Sync Service ✅
- [x] Add syncRelapses method to sync service
- [x] Implement _syncSingleRelapse method
- [x] Update syncAll to include relapse sync

#### 1.5 Update UI ✅
- [x] Update `relapse_page.dart` with sync status widget
- [x] Handle offline states in UI
- [x] Add offline indicators

### Phase 2: Manager Instance Features

#### 2.1 Companion Feature ✅
Location: `lib/src/manager/instance_features/companion/`

##### Tasks:
- [x] Create `data/local/models/instance_companion_local_model.dart`
- [x] Create `data/local/instance_companion_local_datasource.dart`
- [x] Update `data/repository/companion_repository_impl.dart`
  - Add offline support to all methods
  - Integrate local datasource
  - Use MemberLocalDataSource for offline companion creation
- [x] Update existing blocs to handle offline states:
  - [x] `create_companion/` - Add offline status and ConnectionChecker
  - [x] Handle offline deletion in repository
  - [x] Return local data when offline in getCompanionList
- [x] Create `ManagerCompanionSyncHandler` for synchronization
- [x] Update sync service to include companion sync
- [ ] Update UI components:
  - [ ] `companion_list_page.dart`
  - [ ] `add_companion_form.dart`

#### 2.2 Diagnostic Feature
Location: `lib/src/manager/instance_features/diagnostic/`

##### Tasks:
- [ ] Create `data/local/models/diagnostic_local_model.dart`
- [ ] Create `data/local/diagnostic_local_datasource.dart`
- [ ] Update `data/repository/diagnostic_repository_impl.dart`
  - Add offline support to all methods
  - Integrate local datasource
- [ ] Update existing blocs to handle offline states:
  - [ ] `create_diagnostic/` - Add offline status
  - [ ] `edit_diagnostic/` - Handle offline editing
  - [ ] `delete_diagnostic/` - Handle offline deletion
  - [ ] `get_diagnostics/` - Return local data when offline
- [ ] Update UI components:
  - [ ] `diagnostic_list_page.dart`
  - [ ] `diagnostic_form.dart`


#### 2.5 Relapse Feature (Manager)
Location: `lib/src/manager/instance_features/relapse/`

##### Tasks:
- [ ] Create `data/local/models/relapse_local_model.dart`
- [ ] Create `data/local/relapse_local_datasource.dart`
- [ ] Update `data/repository/relapse_repository_impl.dart`
  - Add offline support to all methods
  - Integrate local datasource
- [ ] Update existing bloc:
  - [ ] Extend `relapse_cubit.dart` with offline support
  - [ ] Update `relapse_state.dart` with offline states
- [ ] Update UI (if any pages exist)

#### 2.5 Treatment Feature
Location: `lib/src/manager/instance_features/treatment/`

##### Tasks:
- [ ] Create `data/local/models/treatment_local_model.dart`
- [ ] Create `data/local/treatment_local_datasource.dart`
- [ ] Update `data/repository/treatment_repository_impl.dart`
  - Add offline support to all methods
  - Integrate local datasource
- [ ] Update existing blocs to handle offline states:
  - [ ] `create_treatment/` - Add offline status
  - [ ] `edit_treatment/` - Handle offline editing
  - [ ] `delete_treatment/` - Handle offline deletion
  - [ ] `get_treatment/` - Return local data when offline
  - [ ] `get_treatment_list/` - Return local data when offline
- [ ] Update UI components:
  - [ ] `treatment_list_page.dart`
  - [ ] `treatment_form.dart`
  - [ ] `treatment_detail.dart`

### Phase 3: Integration Tasks

#### 3.1 Sync Service Updates
- [ ] Update `lib/src/sync/sync_service.dart`:
  - [ ] Add sync methods for each new feature
  - [ ] Update `syncAll()` method
  - [ ] Add feature-specific sync error handling
  - [ ] Import new sync handlers

#### 3.2 Dependency Injection
- [ ] Update `lib/core/container/injectable.config.dart`:
  - [ ] Register all new local datasources
  - [ ] Update repository registrations with new dependencies
  - [ ] No need to register separate offline repositories/use cases/blocs

#### 3.3 ObjectBox Schema
- [ ] Update ObjectBox model for all new entities
- [ ] Run ObjectBox code generation
- [ ] Test schema migrations

### Phase 4: Testing & Refinement

#### 4.1 Unit Tests
- [ ] Test local datasources
- [ ] Test offline repositories
- [ ] Test sync logic
- [ ] Test conflict resolution

#### 4.2 Integration Tests
- [ ] Test offline/online transitions
- [ ] Test sync queue processing
- [ ] Test data consistency

#### 4.3 UI/UX Testing
- [ ] Test offline indicators
- [ ] Test sync status feedback
- [ ] Test error handling
- [ ] Test user workflows offline

## Implementation Guidelines

### 1. Dual Delete Pattern (CRITICAL)
Every local datasource MUST implement both physical and soft delete methods:

```dart
abstract class EntityLocalDataSource {
  // Physical deletion - removes from local storage completely
  // Used by: Sync handlers after successful server sync
  Future<void> deleteEntity(String localId);
  Future<void> deleteEntityById(String id);

  // Soft deletion - marks as deleted but keeps for sync
  // Used by: Repositories when offline to mark for later sync
  Future<void> markEntityAsDeleted(String localId);
  Future<void> markEntityAsDeletedById(String id);
}
```

**Usage Pattern:**
- **Repositories (when online)**: Try server delete → if successful, call `deleteEntityById()`
- **Repositories (when offline)**: Call `markEntityAsDeleted()` to flag for sync
- **Sync Handlers**: Check `isDeleted` → sync to server → call `deleteEntity()` to physically remove

### 2. Repository Implementation Pattern
Each repository should handle both online and offline cases:
```dart
@Injectable(as: EntityRepository)
class EntityRepositoryImpl implements EntityRepository {
  final EntityRemoteDataSource _remoteDataSource;
  final EntityLocalDataSource _localDataSource;
  final ConnectionChecker _connectionChecker;
  final SyncService _syncService;
  
  @override
  RepositoryResponse<Entity> createEntity(...) async {
    final isOnline = await _connectionChecker.isOnline();
    
    if (isOnline) {
      // Try server first, save to local on success
      final response = await requestHelper(() => _remoteDataSource.create(...));
      if (response.isRight()) {
        // Save to local as synced
      }
      return response;
    } else {
      // Create locally with pending status
      final localEntity = EntityLocalModel.createOffline(...);
      await _localDataSource.save(localEntity);
      return Right(localEntity.toEntity());
    }
  }
}
```

### 2. Local Model Structure
Each local model must include:
```dart
@Entity()
class EntityLocalModel {
  @Id()
  int oid = 0;
  
  @Index()
  @Unique()
  String? id; // Server ID, null for offline-created entities
  
  @Index()
  @Unique()
  String localId; // Local UUID for identification
  
  String syncStatus; // SyncStatus enum as string (import from 'package:s3g/src/sync/sync_service.dart')
  DateTime? lastSyncAttempt;
  String? syncError;
  bool isDeleted;
  
  @Property(type: PropertyType.date)
  DateTime createdAt;
  
  @Property(type: PropertyType.date)
  DateTime updatedAt;
  
  // Entity-specific fields...
  
  // REQUIRED: Helper getter for sync check
  bool get needsSync =>
      SyncStatus.fromString(syncStatus) == SyncStatus.pending ||
      SyncStatus.fromString(syncStatus) == SyncStatus.failed;
  
  // REQUIRED: Helper getter for offline created check
  bool get isOfflineCreated => id == null;
  
  // REQUIRED: Helper getter for display ID
  String get displayId => id ?? localId;
}
```

### 3. Handling Unique Constraint Violations
All local datasources MUST handle unique constraints properly to avoid ObjectBoxException:
```dart
@override
Future<void> saveEntity(EntityLocalModel entity) async {
  // Check if an entity with this server ID already exists
  if (entity.id != null) {
    final query = _box.query(EntityLocalModel_.id.equals(entity.id!)).build();
    final existing = await query.findFirstAsync();
    query.close();
    
    if (existing != null) {
      // Update the existing record
      existing.field1 = entity.field1;
      existing.field2 = entity.field2;
      // ... update all fields
      existing.updatedAt = entity.updatedAt;
      existing.syncStatus = entity.syncStatus;
      await _box.putAsync(existing);
      return;
    }
  }
  
  // No existing record found, save as new
  await _box.putAsync(entity);
}

// For bulk operations
@override
Future<void> saveEntities(List<EntityLocalModel> entities) async {
  // Process each entity individually to handle updates
  for (final entity in entities) {
    await saveEntity(entity);
  }
}
```

### 4. Sync Handler Implementation Pattern
All sync handlers MUST follow this pattern for consistency:

```dart
Future<void> _syncSingleEntity(EntityLocalModel localEntity) async {
  try {
    if (localEntity.isDeleted) {
      // Handle deletion first
      if (localEntity.id != null) {
        // Delete from server if it exists there
        try {
          await _remoteDataSource.deleteEntity(entityId: localEntity.id!);
        } on DioException catch (e) {
          if (e.response?.statusCode != 404) rethrow; // Ignore 404s
        }
      }
      // Physical removal from local storage
      await _localDataSource.deleteEntity(localEntity.localId);
      return;
    }

    if (localEntity.isOfflineCreated) {
      // Create on server
      final result = await _remoteDataSource.createEntity(/* params */);
      // Use helper method for sync status update
      await _localDataSource.markEntityAsSynced(localEntity.localId, result.id);
    } else {
      // Update on server
      await _remoteDataSource.updateEntity(/* params */);
      // Use helper method for sync status update
      localEntity.updateSyncStatus(SyncStatus.synced);
      await _localDataSource.saveEntity(localEntity);
    }
  } catch (e) {
    // Use helper method for error handling
    await _localDataSource.markEntitySyncFailed(localEntity.localId, e.toString());
  }
}
```

### 5. Required Model Methods
Each local model should implement these methods following the pattern from FollowupLocalModel:
```dart
// Factory method to create from domain entity
factory EntityLocalModel.fromEntity(Entity entity) {
  return EntityLocalModel(
    id: entity.id,
    // ... map all fields
    syncStatus: SyncStatus.synced.value,
  );
}

// Factory method to create offline entity
factory EntityLocalModel.createOffline({required fields...}) {
  final now = DateTime.now();
  return EntityLocalModel(
    // ... fields
    createdAt: now,
    updatedAt: now,
    syncStatus: SyncStatus.pending.value,
    localId: const Uuid().v4(),
  );
}

// Convert to domain entity
Entity toEntity() {
  return Entity(
    id: id ?? localId, // Use localId if no server ID
    // ... map all fields
  );
}

// Update sync status
void updateSyncStatus(SyncStatus status, {String? error}) {
  syncStatus = status.value;
  lastSyncAttempt = DateTime.now();
  syncError = error;
}

// Mark as synced with server ID
void markAsSynced(String serverId) {
  id = serverId;
  syncStatus = SyncStatus.synced.value;
  lastSyncAttempt = DateTime.now();
  syncError = null;
}

// Update content and mark for sync
void updateContent({required fields...}) {
  // Update fields
  updatedAt = DateTime.now();
  
  // Mark as needing sync if already synced
  if (SyncStatus.fromString(syncStatus) == SyncStatus.synced) {
    syncStatus = SyncStatus.pending.value;
  }
}
```

### 6. Required Local DataSource Helper Methods
All local datasources MUST implement these helper methods for consistency:

```dart
abstract class EntityLocalDataSource {
  // Sync status management
  Future<void> markEntityAsSynced(String localId, String serverId);
  Future<void> markEntitySyncFailed(String localId, String error);

  // Sync queries - CRITICAL: Do NOT filter out deleted items!
  Future<List<EntityLocalModel>> getPendingSyncEntities() async {
    // ✅ CORRECT - Include deleted items for sync
    final query = _box.query(
      EntityLocalModel_.syncStatus.equals(SyncStatus.pending.value) |
      EntityLocalModel_.syncStatus.equals(SyncStatus.failed.value)
    ).build();

    // ❌ WRONG - Never add: & EntityLocalModel_.isDeleted.equals(false)
    // Deleted items MUST sync to inform server about deletion
  }

  // Content updates
  Future<void> updateEntityContent(String localId, {/* fields */});
  Future<void> updateEntityFromServer(EntityLocalModel serverEntity);
}
```

### 7. Bloc/Cubit Updates
Blocs should be updated to:
- Include offline status in success states
- Inject ConnectionChecker for status awareness
- Display appropriate messages based on connection state

Example:
```dart
final class CreateEntitySuccess extends CreateEntityState {
  final Entity entity;
  final bool isOffline;
  
  CreateEntitySuccess({required this.entity, this.isOffline = false});
}
```

### 6. UI Considerations
- Always show sync status widget
- Provide clear offline indicators
- Show optimistic UI updates
- Handle sync errors gracefully

### 8. Instance Feature Dependency Pattern (CRITICAL)
For all instance features (companion, diagnostic, treatment, relapse in manager namespace):

```dart
// In sync handler - ALWAYS check parent instance first
Future<void> syncFeature(FeatureLocalModel feature) async {
  // Get the instance by localId (not server ID!)
  final instance = await _instanceLocalDataSource.getInstanceByLocalId(
    feature.instanceLocalId
  );

  if (instance == null) {
    log('Instance not found for feature ${feature.localId}');
    return; // Skip this feature
  }

  // CRITICAL: Check if instance is synced before syncing feature
  if (instance.syncStatus != SyncStatus.synced.value) {
    log('Instance ${instance.localId} not synced yet, skipping feature');
    return; // Block feature sync until instance is synced
  }

  // Now safe to sync feature using instance.id (server ID)
  final response = await _remoteDataSource.createFeature(
    instanceId: instance.id!, // Use server ID for API
    /* other params */
  );
}
```

### 9. Error Handling
- Distinguish between offline and other errors
- Implement retry logic for failed syncs
- Log sync errors for debugging
- Provide user-friendly error messages in French (non-technical)

## Issues Found in Existing Implementations

### Critical Issues to Fix:

#### 1. Inconsistent Delete Method Interfaces
**Problem**: Not all datasources follow the dual delete pattern.

**Affected Files**:
- `lib/src/companion/relapse/data/local/relapse_local_datasource.dart` - Missing `deleteRelapseById` and `markRelapseAsDeletedById`
- `lib/src/manager/instance_features/relapse/data/local/relapse_local_datasource.dart` - Missing `markRelapseAsDeleted` methods
- `lib/src/companion/companion/data/local/companion_local_datasource.dart` - Only has soft delete, missing physical delete methods

**Fix Required**: Add missing methods to match the standard pattern.

#### 2. Missing Helper Methods in Local Models
**Problem**: Some local models lack essential helper methods.

**Affected Files**:
- `lib/src/manager/instance_features/relapse/data/local/models/relapse_local_model.dart`
- `lib/src/companion/relapse/data/local/models/relapse_local_model.dart`

**Missing Methods**:
```dart
// Add these to all local models
bool get needsSync => SyncStatus.fromString(syncStatus) == SyncStatus.pending ||
                     SyncStatus.fromString(syncStatus) == SyncStatus.failed;
bool get isOfflineCreated => id == null;
String get displayId => id ?? localId;
void markAsSynced(String serverId) { /* implementation */ }
void updateSyncStatus(SyncStatus status, {String? error}) { /* implementation */ }
```

#### 3. Inconsistent Sync Handler Patterns
**Problem**: Some sync handlers manually set sync fields instead of using helper methods.

**Affected Files**:
- `lib/src/sync/handlers/companion_relapse_sync_handler.dart`
- `lib/src/sync/handlers/manager_companion_sync_handler.dart`

**Fix Required**: Replace manual field setting with datasource helper methods.

#### 4. Missing Offline Support
**Problem**: Some repositories lack offline support entirely.

**Affected Files**:
- `lib/src/manager/instance_features/relapse/data/repository/relapse_repository_impl.dart`
- Most manager instance feature repositories

**Fix Required**: Add connection checking and offline fallback logic.

## Quick Implementation Checklist

When implementing offline support for a new feature:

1. **Create Local Model** (`*_local_model.dart`)
   - [ ] Extend from ObjectBox `@Entity()`
   - [ ] Add `@Unique()` to `id` field
   - [ ] Include all sync fields (syncStatus, localId, etc.)
   - [ ] Add helper getters (needsSync, isOfflineCreated, displayId)
   - [ ] Implement factory methods (fromEntity, createOffline)
   - [ ] Add update methods (updateContent, markAsSynced)

2. **Create Local DataSource** (`*_local_datasource.dart`)
   - [ ] Define abstract interface with BOTH delete patterns:
     - [ ] `deleteEntity(String localId)` - Physical removal
     - [ ] `deleteEntityById(String id)` - Physical removal by server ID
     - [ ] `markEntityAsDeleted(String localId)` - Soft delete
     - [ ] `markEntityAsDeletedById(String id)` - Soft delete by server ID
   - [ ] Implement save with duplicate checking
   - [ ] Add CRUD operations
   - [ ] Include sync helper methods:
     - [ ] `markEntityAsSynced(String localId, String serverId)`
     - [ ] `markEntitySyncFailed(String localId, String error)`
   - [ ] CRITICAL: `getPendingSync*` methods must NOT filter by `isDeleted.equals(false)`

3. **Update Repository**
   - [ ] Add local datasource dependency
   - [ ] Add connection checker dependency
   - [ ] Implement offline fallback for all methods
   - [ ] Save to local on successful server calls
   - [ ] Use French error messages

4. **Create/Update Sync Handler** (REQUIRED for all sync-enabled entities)
   - [ ] Create handler in `lib/src/sync/handlers/`
   - [ ] Extend BaseSyncHandler
   - [ ] Implement syncAll method following the standard pattern:
     - [ ] Check `isDeleted` flag first
     - [ ] Use datasource helper methods (`markEntityAsSynced`, `markEntitySyncFailed`)
     - [ ] Handle 404 errors gracefully for deletions
     - [ ] For instance features: check parent instance sync status first
   - [ ] Handle conflicts appropriately
   - [ ] Use proper naming (CompanionXSyncHandler or ManagerXSyncHandler)
   - [ ] Register in sync_service.dart
   - [ ] Add to syncAll() method

5. **Update UI**
   - [ ] Add SyncStatusWidget
   - [ ] Handle offline states in BLoCs
   - [ ] Show offline indicators
   - [ ] Display sync errors appropriately

## Success Criteria
- [ ] All features work seamlessly offline
- [ ] Data syncs automatically when online
- [ ] No data loss during offline/online transitions
- [ ] Clear user feedback about sync status
- [ ] Consistent offline behavior across features

## Timeline Estimate
- Phase 1 (Relapse - Companion): 2 days
- Phase 2 (Manager Features): 8-10 days
  - Companion: 2 days
  - Diagnostic: 2 days
  - Followup: 1.5 days
  - Relapse: 1.5 days
  - Treatment: 2 days
- Phase 3 (Integration): 2 days
- Phase 4 (Testing): 3 days

**Total Estimated Time**: 15-17 days

## Code Reuse and Pattern Guidelines

### Base Classes and Shared Code
To avoid code repetition, consider creating these shared components:

1. **BaseLocalDataSource**
```dart
abstract class BaseLocalDataSource<T> {
  final Box<T> box;
  
  Future<void> saveEntity(T entity) async {
    // Implement the common save pattern with duplicate checking
  }
  
  Future<void> saveEntities(List<T> entities) async {
    for (final entity in entities) {
      await saveEntity(entity);
    }
  }
}
```

2. **BaseLocalModel**
```dart
abstract class BaseLocalModel {
  String? get id;
  String get localId;
  String get syncStatus;
  DateTime? get lastSyncAttempt;
  String? get syncError;
  bool get isDeleted;
  DateTime get createdAt;
  DateTime get updatedAt;
  
  bool get needsSync =>
      SyncStatus.fromString(syncStatus) == SyncStatus.pending ||
      SyncStatus.fromString(syncStatus) == SyncStatus.failed;
  
  bool get isOfflineCreated => id == null;
  String get displayId => id ?? localId;
}
```

3. **Repository Helper Methods**
Create a mixin or utility class:
```dart
mixin OfflineRepositoryMixin {
  Future<Either<Failure, T>> handleOfflineOperation<T>({
    required Future<T> Function() onlineOperation,
    required Future<T> Function() offlineOperation,
    required ConnectionChecker connectionChecker,
  }) async {
    final isOnline = await connectionChecker.isOnline();
    
    try {
      if (isOnline) {
        return Right(await onlineOperation());
      } else {
        return Right(await offlineOperation());
      }
    } catch (e) {
      return Left(DatabaseFailure('Erreur de base de données'));
    }
  }
}
```

4. **Common Error Messages**
Create a constants file:
```dart
class ErrorMessages {
  static const String noInternetConnection = 'Aucune connexion Internet';
  static const String noOfflineData = 'Aucune donnée disponible hors ligne';
  static const String syncError = 'Erreur de synchronisation';
  static const String databaseError = 'Erreur de base de données';
  static const String notFound = 'Élément introuvable';
  // Add more as needed
}
```

### Reusable Patterns

1. **Sync Handler Base Class**
Already exists as `BaseSyncHandler` - extend it for all sync handlers.

2. **Repository Pattern**
Use the same structure for all repositories:
- Check connection status
- Handle online/offline scenarios
- Save to local storage on success
- Return appropriate errors

3. **Model Conversion**
Standardize the conversion pattern:
- `fromEntity` - Domain to local model
- `toEntity` - Local model to domain
- `createOffline` - Create new offline entity
- Use existing remote models' `fromEntity` methods for JSON conversion

### What NOT to Repeat

1. **Don't duplicate sync logic** - Use the sync service
2. **Don't duplicate error handling** - Use the request helper
3. **Don't duplicate connection checking** - Inject ConnectionChecker
4. **Don't duplicate model conversion logic** - Use factory methods
5. **Don't duplicate ObjectBox setup** - It's already in core

### Existing Reusable Components

1. **Core Infrastructure** (Already Available)
   - `ObjectBox` - Database setup in `lib/core/objectbox/`
   - `ConnectionChecker` - Network status checking
   - `SyncService` - Central sync orchestration in `lib/src/sync/`
   - `BaseSyncHandler` - Base class for sync handlers in `lib/src/sync/handlers/`
   - `requestHelper` - Error handling for API calls
   - `SyncStatus` enum - In `lib/src/sync/status.dart` (exported by sync_service.dart)

2. **Widgets** (Ready to Use)
   - `SyncStatusWidget` - Shows sync status
   - `OfflineDemoWidget` - Demo offline capabilities
   - Error display widgets

3. **Patterns to Copy**
   - Repository implementation from `FollowupRepositoryImpl`
   - Local model structure from `FollowupLocalModel`
   - Datasource pattern from `FollowupLocalDataSource`
   - Sync handler pattern from `CompanionFollowupSyncHandler`

4. **Utilities**
   - UUID generation: `const Uuid().v4()`
   - JSON encoding/decoding with model classes
   - DateTime handling for sync timestamps

## Important Implementation Notes

### Critical Requirements
1. **Unique Constraints**: All models with server IDs must have `@Unique()` annotation on the `id` field
2. **Sync Status**: All mutable models must track sync status with `needsSync` getter
3. **Local IDs**: Use UUID v4 for local IDs to ensure uniqueness
4. **Error Messages**: All user-facing errors must be in French and non-technical
5. **Duplicate Handling**: Always check for existing records before inserting to avoid UniqueViolationException
6. **Sync Handlers**: EVERY entity that needs synchronization MUST have a corresponding sync handler
7. **Import SyncStatus**: Always import from `'package:s3g/src/sync/sync_service.dart'`

### Reference Implementation
Use `lib/src/companion/followup/data/local/models/followup_local_model.dart` as the reference for:
- Model structure with all required fields
- Helper getters (needsSync, isOfflineCreated, displayId)
- Factory methods (fromEntity, createOffline)
- Update methods (updateContent, markAsSynced, updateSyncStatus)

### When to Create Sync Handlers
Create a sync handler for any entity that:
- Can be created/updated/deleted offline
- Has a `needsSync` getter in its local model
- Needs to synchronize with the server
- Has CRUD operations in its repository

Do NOT create sync handlers for:
- Read-only entities (e.g., HealthCenter if only fetched)
- Entities that are always online-only
- Nested entities that sync with their parent

### Common Pitfalls to Avoid
1. **Direct putAsync**: Never use `_box.putAsync()` directly without checking for existing records
2. **putMany**: Don't use `putMany` for bulk operations; iterate and handle each record
3. **Missing sync fields**: Ensure all models have sync tracking fields
4. **English errors**: Replace all error messages with French equivalents
5. **Manual JSON mapping**: Use existing model classes with `fromEntity` methods for JSON serialization
6. **Filtering deleted items in getPending**: NEVER filter out deleted items in `getPending*` methods. Deleted items need to sync too!
   ```dart
   // ❌ WRONG - Don't filter out deleted items
   _box.query(
     (EntityLocalModel_.syncStatus.equals(SyncStatus.pending.value) |
      EntityLocalModel_.syncStatus.equals(SyncStatus.failed.value)) &
     EntityLocalModel_.isDeleted.equals(false)  // WRONG! This prevents deletion sync
   ).build();
   
   // ✅ CORRECT - Include deleted items for sync
   _box.query(
     EntityLocalModel_.syncStatus.equals(SyncStatus.pending.value) |
     EntityLocalModel_.syncStatus.equals(SyncStatus.failed.value)
   ).build();
   ```
   **Why**: Deleted items must be synced to inform the server about the deletion. If we filter them out, the server will never know about local deletions.

### Sync Handler Requirements
1. **Location**: All sync handlers must be in `lib/src/sync/handlers/`
2. **Naming Convention**:
   - Companion namespace: `CompanionFeatureSyncHandler` (e.g., CompanionFollowupSyncHandler)
   - Manager namespace: `ManagerFeatureSyncHandler` (e.g., ManagerFollowupSyncHandler)
3. **Registration**: Must be registered in `lib/src/sync/sync_service.dart`
4. **Export**: Add to `lib/src/sync/handlers/sync_handlers.dart` exports

### Instance Features Special Considerations
For all instance features (companion, diagnostic, treatment, relapse in manager namespace):
1. **Instance LocalId Dependency**: Features should use instance's `localId` for relationships
   - Always store the instance localId that was used during creation
   - This ensures the relationship is maintained even before the instance is synced
2. **Sync Blocking**: Do NOT sync a feature if the parent instance hasn't been synced yet
   - Get instance by localId and check if it has `syncStatus == SyncStatus.synced`
   - An instance created offline will not have a server ID until synced
3. **ID Resolution**: When syncing, ensure the instance is synced before syncing its features
4. **Implementation Pattern**:
   ```dart
   // In local model
   String instanceLocalId; // Store the instance localId
   
   // In sync handler
   Future<void> syncFeature(FeatureLocalModel feature) async {
     // Get the instance by localId
     final instance = await _instanceLocalDataSource.getInstanceByLocalId(feature.instanceLocalId);
     
     // Check if instance exists
     if (instance == null) {
       log('Instance not found for feature ${feature.localId}');
       return;
     }
     
     // Check if instance is synced
     if (instance.syncStatus != SyncStatus.synced.value) {
       // Instance not synced yet, skip this feature
       log('Instance ${instance.localId} not synced yet (status: ${instance.syncStatus}), skipping feature sync');
       return;
     }
     
     // Use the instance's server ID for API call
     final response = await _remoteDataSource.create(
       feature.toRemoteModel(instanceId: instance.id!),
     );
   }
   ```

## Notes
- Follow existing patterns from implemented features
- Maintain consistent naming conventions
- Document any deviations from the pattern
- Consider performance implications of ObjectBox queries
- Ensure proper error handling at all levels