import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:s3g/core/config/theme.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/src/companion/companion/companion.dart';
import 'package:s3g/src/manager/instance/instance.dart';

class CompanionDetail extends StatelessWidget {
  const CompanionDetail({super.key});

  @override
  Widget build(BuildContext context) {
    final companion =
        (context.watch<CompanionShowCubit>().state as CompanionShowLoaded)
            .companion;

    return Container(
      color: const Color(0xFFF8FAFC),
      child: RefreshIndicator(
        color: AppTheme.primary,
        onRefresh: () async {
          return context.read<CompanionShowCubit>().getFetchCompanion();
        },
        child: SingleChildScrollView(
          padding: bodyPadding,
          child: _CompanionDetailContent(companion),
        ),
      ),
    );
  }
}

class _CompanionDetailContent extends StatelessWidget {
  final Companion companion;
  const _CompanionDetailContent(this.companion);

  @override
  Widget build(BuildContext context) {
    final date = DateFormat.yMMMd('fr').format(companion.createdAt.toLocal());
    final hasRelapse = companion.instance.relapse != null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header Card
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(cardRadius),
            border: Border.all(
              color: hasRelapse
                  ? AppTheme.primary.withValues(alpha: 0.2)
                  : Colors.grey.withValues(alpha: 0.08),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.04),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: const Color(0xFF3B82F6).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.people,
                      color: Color(0xFF3B82F6),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Survivant: ${companion.instance.survivorCode}",
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF1F2937),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          "Rôle: ${companion.type.getLabel()}",
                          style: const TextStyle(
                            fontSize: 14,
                            color: Color(0xFF059669),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (hasRelapse)
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppTheme.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        "Rechute",
                        style: TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.w600,
                          color: AppTheme.primary,
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),

        const SizedBox(height: 20),

        // Details Cards
        _buildDetailCard(
          icon: Icons.cases_outlined,
          title: "Code Cas",
          content: companion.instance.code,
          color: const Color(0xFF3B82F6),
        ),

        const SizedBox(height: 12),

        _buildDetailCard(
          icon: Icons.difference_outlined,
          title: "Type Cas",
          content: companion.instance.type.getLabel(),
          color: const Color(0xFF059669),
        ),

        const SizedBox(height: 12),

        _buildDetailCard(
          icon: companion.instance.status == InstanceStatus.OPEN
              ? Icons.folder_open
              : Icons.folder_off,
          title: "Statut Cas",
          content: companion.instance.status.getLabel(),
          color: companion.instance.status == InstanceStatus.OPEN
              ? const Color(0xFF059669)
              : Colors.grey[600]!,
        ),

        const SizedBox(height: 12),

        _buildDetailCard(
          icon: Icons.local_hospital_outlined,
          title: "Centre de santé",
          content: companion.instance.healthCenter?.name ?? 'N/A',
          color: const Color(0xFF8B5CF6),
        ),

        if (companion.instance.creator != null) ...[
          const SizedBox(height: 12),
          _buildDetailCard(
            icon: Icons.person_outline,
            title: "APS",
            content:
                "${companion.instance.creator!.name}\n${companion.instance.creator!.phone}",
            color: const Color(0xFF059669),
          ),
        ],

        if (companion.instance.description != null) ...[
          const SizedBox(height: 12),
          _buildDetailCard(
            icon: Icons.description,
            title: "Description du cas",
            content: companion.instance.description!,
            color: const Color(0xFF6B7280),
            isLongText: true,
          ),
        ],

        if (hasRelapse) ...[
          const SizedBox(height: 12),
          _buildDetailCard(
            icon: Icons.trending_down_outlined,
            title: "Rechute signalée le",
            content: DateFormat.yMMMd('fr').format(
              companion.instance.relapse!.createdAt.toLocal(),
            ),
            color: AppTheme.primary,
          ),
        ],

        const SizedBox(height: 12),

        _buildDetailCard(
          icon: Icons.calendar_today,
          title: "Assigné le",
          content: date,
          color: const Color(0xFF6B7280),
        ),
      ],
    );
  }

  Widget _buildDetailCard({
    required IconData icon,
    required String title,
    required String content,
    required Color color,
    bool isLongText = false,
  }) {
    return Container(
      padding: cardSpacing,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.08),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment:
            isLongText ? CrossAxisAlignment.start : CrossAxisAlignment.center,
        children: [
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 18,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF6B7280),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  content,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF1F2937),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
