import 'dart:convert';

import 'package:objectbox/objectbox.dart';
import 'package:s3g/src/companion/companion/domain/entity/companion.dart';
import 'package:s3g/src/manager/instance/instance.dart';
import 'package:s3g/src/manager/member/member.dart';
import 'package:uuid/uuid.dart';

@Entity()
class CompanionLocalModel {
  @Id()
  int oid;

  @Index()
  @Unique()
  String? id; // Server ID, null for offline-created companions

  String type; // MemberCompanionRole as string
  String instanceId;

  @Property(type: PropertyType.date)
  late DateTime createdAt;

  @Property(type: PropertyType.date)
  late DateTime updatedAt;

  @Index()
  @Unique()
  String localId; // Local UUID for identification

  bool isDeleted;

  // Instance data stored as JSON string
  String instanceJson;

  CompanionLocalModel({
    this.oid = 0,
    this.id,
    required this.type,
    required this.instanceId,
    required this.createdAt,
    required this.updatedAt,
    String? localId,
    this.isDeleted = false,
    required this.instanceJson,
  }) : localId = localId ?? const Uuid().v4();

  // Convert from domain entity
  factory CompanionLocalModel.fromEntity(
    Companion companion, {
    required String instanceJson,
  }) {
    return CompanionLocalModel(
      id: companion.id,
      type: companion.type.name,
      instanceId: companion.instance.id,
      createdAt: companion.createdAt,
      updatedAt: companion.updatedAt,
      instanceJson: instanceJson,
    );
  }

  // Create offline companion
  factory CompanionLocalModel.createOffline({
    required MemberCompanionRole type,
    required String instanceId,
    required String instanceJson,
  }) {
    final now = DateTime.now();
    return CompanionLocalModel(
      type: type.name,
      instanceId: instanceId,
      createdAt: now,
      updatedAt: now,
      instanceJson: instanceJson,
    );
  }

  // Convert to domain entity
  Companion toEntity() {
    final instanceMap = jsonDecode(instanceJson) as Map<String, dynamic>;
    final instance = InstanceShowModel.fromJson(instanceMap);

    return Companion(
      id: id ?? localId, // Use localId if no server ID
      type: MemberCompanionRole.values.firstWhere(
        (e) => e.name == type,
      ),
      instance: instance,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  // Check if is offline created
  bool get isOfflineCreated => id == null;

  // Get display ID
  String get displayId => id ?? localId;

  // Update content
  void updateContent({
    MemberCompanionRole? newType,
    String? newInstanceId,
    String? newInstanceJson,
  }) {
    if (newType != null) type = newType.name;
    if (newInstanceId != null) instanceId = newInstanceId;
    if (newInstanceJson != null) instanceJson = newInstanceJson;
    updatedAt = DateTime.now();
  }
}
