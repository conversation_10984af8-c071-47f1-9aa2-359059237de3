import 'dart:convert';

import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/errors/failures.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/network/connection_checker.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/src/companion/companion/data/local/companion_local_datasource.dart';
import 'package:s3g/src/companion/companion/data/local/models/companion_local_model.dart';
import 'package:s3g/src/companion/companion/domain/entity/companion.dart';

import '../../domain/repository/companion_repository.dart';
import '../remote/companion_remote_datasource.dart';

@Injectable(as: CompanionRepository)
class CompanionRepositoryImpl extends CompanionRepository {
  final CompanionRemoteDataSource _remoteDataSource;
  final CompanionLocalDataSource _localDataSource;
  final ConnectionChecker _connectionChecker;

  CompanionRepositoryImpl(
    this._remoteDataSource,
    this._localDataSource,
    this._connectionChecker,
  );

  @override
  RepositoryResponse<Companion> getCompanion(String companionId) async {
    final isOnline = await _connectionChecker.isOnline();

    if (isOnline) {
      // Try to get from server
      final response = await requestHelper(
        () => _remoteDataSource.getCompanion(companionId),
      );

      if (response.isRight()) {
        // Save to local database for offline access
        response.fold(
          (failure) => null,
          (companion) async {
            // Convert instance to JSON for offline storage
            final instanceJson = jsonEncode(companion.instance.toJson());
            final localCompanion = CompanionLocalModel.fromEntity(
              companion,
              instanceJson: instanceJson,
            );
            await _localDataSource.saveCompanion(localCompanion);
          },
        );
      }

      return response;
    } else {
      // Offline: try to get from local database
      final localCompanion =
          await _localDataSource.getCompanionById(companionId);

      if (localCompanion != null) {
        // Successfully reconstruct the companion with its instance
        return Right(localCompanion.toEntity());
      } else {
        return Left(CacheFailure(
          'Aucune donnée disponible hors ligne',
        ));
      }
    }
  }

  @override
  RepositoryResponse<Paginated<Companion>> getCompanions(int? page) async {
    final isOnline = await _connectionChecker.isOnline();

    if (isOnline) {
      // Online: get from server and save to local
      final response =
          await requestHelper(() => _remoteDataSource.getCompanions(page));

      if (response.isRight()) {
        // Save to local database for offline access
        response.fold(
          (failure) => null,
          (paginated) async {
            final localCompanions = await Future.wait(
              paginated.data.map((companion) async {
                // Convert instance to JSON for offline storage
                final instanceJson = jsonEncode(companion.instance.toJson());

                return CompanionLocalModel.fromEntity(
                  companion,
                  instanceJson: instanceJson,
                );
              }),
            );
            await _localDataSource.saveCompanions(localCompanions);
          },
        );
      }

      return response;
    } else {
      // Offline: get from local database
      final localCompanions = await _localDataSource.getAllCompanions();

      if (localCompanions.isEmpty) {
        return Left(CacheFailure(
          'Aucune donnée disponible hors ligne',
        ));
      }

      try {
        // Reconstruct companions with their instances
        final companions = localCompanions
            .where((c) => !c.isDeleted)
            .map((localCompanion) => localCompanion.toEntity())
            .toList();

        // Create a paginated response
        final paginatedResponse = Paginated<Companion>.create(companions);

        return Right(paginatedResponse);
      } catch (e) {
        return Left(DatabaseFailure(
          'Erreur lors de la reconstruction des données locales',
        ));
      }
    }
  }
}
