part of 'relapse_cubit.dart';

@immutable
sealed class RelapseState {}

final class RelapseInitial extends RelapseState {}

final class RelapseLoading extends RelapseState {}

enum SuccessType {
  create,
  update,
  delete,
  get,
}

final class RelapseSuccess extends RelapseState {
  final Relapse? relapse;
  final bool isOffline;
  final SuccessType? successType;

  RelapseSuccess({
    this.relapse,
    this.isOffline = false,
    this.successType,
  });
}

final class RelapseError extends RelapseState {
  final String message;

  RelapseError({required this.message});
}
