import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/core/network/connection_checker.dart';
import 'package:s3g/src/manager/instance_features/relapse/relapse.dart';

import '../../domain/usecase/create_relapse.dart';
import '../../domain/usecase/delete_relapse.dart';
import '../../domain/usecase/get_relapse.dart';
import '../../domain/usecase/update_relapse.dart';

part 'relapse_state.dart';

class RelapseCubit extends Cubit<RelapseState> {
  final CreateRelapse createRelapseUseCase;
  final DeleteRelapse deleteRelapseUseCase;
  final UpdateRelapse updateRelapseUseCase;
  final GetRelapse getRelapseUseCase;
  final ConnectionChecker connectionChecker;
  final String companionId;

  RelapseCubit(
    this.companionId, {
    required this.createRelapseUseCase,
    required this.deleteRelapseUseCase,
    required this.updateRelapseUseCase,
    required this.getRelapseUseCase,
    required this.connectionChecker,
  }) : super(RelapseInitial());

  Future<void> getRelapse() async {
    emit(RelapseLoading());

    final isOnline = await connectionChecker.isOnline();
    final result = await getRelapseUseCase(
      GetRelapseParams(companionId: companionId),
    );

    result.fold(
      (_) => emit(RelapseSuccess(
        relapse: null,
        isOffline: !isOnline,
        successType: SuccessType.get,
      )),
      (success) => emit(RelapseSuccess(
        relapse: success,
        isOffline: !isOnline,
        successType: SuccessType.get,
      )),
    );
  }

  Future<void> createRelapse({required String description}) async {
    emit(RelapseLoading());

    final isOnline = await connectionChecker.isOnline();
    final result = await createRelapseUseCase(
      CreateRelapseParams(
        companionId: companionId,
        description: description,
      ),
    );

    result.fold(
      (failure) => emit(RelapseError(message: failure.message)),
      (success) => emit(RelapseSuccess(
        relapse: success,
        isOffline: !isOnline,
        successType: SuccessType.create,
      )),
    );
  }

  Future<void> deleteRelapse() async {
    emit(RelapseLoading());

    final isOnline = await connectionChecker.isOnline();
    final result = await deleteRelapseUseCase(
      DeleteRelapseParams(companionId: companionId),
    );

    result.fold(
      (failure) => emit(RelapseError(message: failure.message)),
      (success) => emit(RelapseSuccess(isOffline: !isOnline)),
    );
  }

  Future<void> updateRelapse({required String description}) async {
    emit(RelapseLoading());

    final isOnline = await connectionChecker.isOnline();
    final result = await updateRelapseUseCase(
      UpdateRelapseParams(
        companionId: companionId,
        description: description,
      ),
    );

    result.fold(
      (failure) => emit(RelapseError(message: failure.message)),
      (success) => emit(RelapseSuccess(
        relapse: success,
        isOffline: !isOnline,
        successType: SuccessType.update,
      )),
    );
  }
}
