import 'dart:developer';

import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/errors/failures.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/network/connection_checker.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/src/companion/companion/companion.dart'
    show CompanionLocalDataSource;
import 'package:s3g/src/sync/sync_service.dart';
import 'package:s3g/src/companion/relapse/data/local/models/relapse_local_model.dart';
import 'package:s3g/src/companion/relapse/data/local/relapse_local_datasource.dart';

import 'package:s3g/src/manager/instance_features/relapse/relapse.dart'
    show RelapseModel, Relapse;

import '../../domain/repository/relapse_repository.dart';
import '../remote/relapse_remote_datasource.dart';

@Injectable(as: RelapseRepository)
class RelapseRepositoryImpl extends RelapseRepository {
  final RelapseRemoteDataSource _remoteDataSource;
  final RelapseLocalDataSource _localDataSource;
  final CompanionLocalDataSource _companionLocalDataSource;
  final ConnectionChecker _connectionChecker;
  final SyncService _syncService;

  RelapseRepositoryImpl({
    required RelapseRemoteDataSource remoteDataSource,
    required RelapseLocalDataSource localDataSource,
    required CompanionLocalDataSource companionLocalDataSource,
    required ConnectionChecker connectionChecker,
    required SyncService syncService,
  })  : _remoteDataSource = remoteDataSource,
        _localDataSource = localDataSource,
        _companionLocalDataSource = companionLocalDataSource,
        _connectionChecker = connectionChecker,
        _syncService = syncService;

  @override
  RepositoryResponse<Relapse> createRelapse({
    required String companionId,
    required String description,
  }) async {
    try {
      // Check if device is online
      final isOnline = await _connectionChecker.isOnline();

      if (isOnline) {
        // Try to create on server first
        final response = await requestHelper(
          () => _remoteDataSource.createRelapse(
            companionId: companionId,
            description: description,
          ),
        );

        // If successful, save to local storage as synced
        if (response.isRight()) {
          final relapse = response.getOrElse((l) => throw l);

          final localModel =
              CompanionRelapseLocalModel.fromEntity(relapse, companionId);

          await _localDataSource.saveRelapse(localModel);
        }

        return response;
      } else {
        // Device is offline, create locally
        log('Device offline, creating relapse locally');

        final offlineRelapse = CompanionRelapseLocalModel.createOffline(
          companionId: companionId,
          description: description,
        );

        // Add relapse to companion instance
        final relapseModel = RelapseModel.fromEntity(offlineRelapse.toEntity());

        await _localDataSource.saveRelapse(offlineRelapse);

        await _companionLocalDataSource.addRelapseToCompanionInstance(
          companionId,
          relapseModel,
        );

        // Return as entity
        return Right(offlineRelapse.toEntity());
      }
    } catch (e) {
      log('Error creating relapse: $e');
      return Left(
          ServerFailure('Une erreur s\'est produite : ${e.toString()}'));
    }
  }

  @override
  RepositoryResponse<MessageResponse> deleteRelapse({
    required String companionId,
  }) async {
    try {
      // Check if device is online
      final isOnline = await _connectionChecker.isOnline();

      if (isOnline) {
        // Try to delete on server first
        final response = await requestHelper(
          () => _remoteDataSource.deleteRelapse(companionId: companionId),
        );

        // If successful, delete from local storage
        if (response.isRight()) {
          await _localDataSource.deleteRelapseByCompanionId(companionId);
        }

        return response;
      } else {
        // Device is offline, mark as deleted locally
        log('Device offline, marking relapse as deleted locally');

        final localRelapse =
            await _localDataSource.getRelapseByCompanionId(companionId);

        if (localRelapse != null) {
          // Mark as deleted
          await _localDataSource.markRelapseAsDeleted(localRelapse.localId);

          // Remove relapse from companion instance
          await _companionLocalDataSource
              .removeRelapseFromCompanionInstance(companionId);

          // Trigger sync in background
          _syncService.syncCompanionRelapses().catchError((e) {
            log('Failed to sync relapse deletion immediately: $e');
          });

          return Right(MessageResponse(message: 'Relapse deleted locally'));
        } else {
          return Left(ServerFailure('Rechute introuvable'));
        }
      }
    } catch (e) {
      log('Error deleting relapse: $e');
      return Left(
          ServerFailure('Une erreur s\'est produite : ${e.toString()}'));
    }
  }

  @override
  RepositoryResponse<Relapse> getRelapse({required String companionId}) async {
    try {
      // First, try to get from local storage
      final localRelapse =
          await _localDataSource.getRelapseByCompanionId(companionId);

      // Check if device is online
      final isOnline = await _connectionChecker.isOnline();

      if (isOnline) {
        // First, sync any pending local changes to avoid overwriting them
        try {
          await _syncService.syncCompanionRelapses();
        } catch (e) {
          log('Warning: Failed to sync pending relapses before fetch: $e');
          // Continue with fetch even if sync fails
        }

        // Try to get fresh data from server
        final response = await requestHelper(
          () => _remoteDataSource.getRelapse(companionId: companionId),
        );

        // If successful, update local storage
        if (response.isRight()) {
          final relapse = response.getOrElse((l) => throw l);

          final updatedLocalModel =
              CompanionRelapseLocalModel.fromEntity(relapse, companionId);

          updatedLocalModel.syncStatus = SyncStatus.synced.value;

          await _localDataSource.updateRelapseFromServer(updatedLocalModel);
        }

        return response;
      } else {
        // Sometime the relapse is not in the local storage but the companion has
        // a relapse in its instance. In that case, we need to create a local
        // relapse from the companion instance.
        final companion =
            await _companionLocalDataSource.getCompanionById(companionId);

        if (localRelapse == null && companion != null) {
          final companionEntity = companion.toEntity();
          final relapse = companionEntity.instance.relapse;

          if (relapse != null) {
            final localModel =
                CompanionRelapseLocalModel.fromEntity(relapse, companionId);

            await _localDataSource.saveRelapse(localModel);
            return Right(relapse);
          }
        }

        // Device is offline, return from local storage
        if (localRelapse != null && !localRelapse.isDeleted) {
          return Right(localRelapse.toEntity());
        } else {
          return Left(
              ServerFailure('Rechute introuvable dans le stockage local'));
        }
      }
    } catch (e) {
      log('Error getting relapse: $e');
      return Left(
          ServerFailure('Une erreur s\'est produite : ${e.toString()}'));
    }
  }

  @override
  RepositoryResponse<Relapse> updateRelapse({
    required String companionId,
    required String description,
  }) async {
    try {
      // Check if device is online
      final isOnline = await _connectionChecker.isOnline();

      if (isOnline) {
        // Try to update on server first
        final response = await requestHelper(
          () => _remoteDataSource.updateRelapse(
            companionId: companionId,
            description: description,
          ),
        );

        // If successful, update local storage
        if (response.isRight()) {
          final relapse = response.getOrElse((l) => throw l);
          final localModel =
              CompanionRelapseLocalModel.fromEntity(relapse, companionId);
          localModel.syncStatus = SyncStatus.synced.value;
          await _localDataSource.updateRelapseFromServer(localModel);
        }

        return response;
      } else {
        // Device is offline, update locally
        log('Device offline, updating relapse locally');

        // Find the relapse
        final localRelapse =
            await _localDataSource.getRelapseByCompanionId(companionId);

        if (localRelapse == null) {
          return Left(ServerFailure('Rechute introuvable'));
        }

        // Update the relapse fields
        localRelapse.description = description;
        localRelapse.updatedAt = DateTime.now();

        // Mark as needing sync if already synced
        localRelapse.syncStatus = SyncStatus.pending.value;

        await _localDataSource.saveRelapse(localRelapse);

        // Trigger sync in background
        _syncService.syncCompanionRelapses().catchError((e) {
          log('Failed to sync relapse immediately: $e');
        });

        return Right(localRelapse.toEntity());
      }
    } catch (e) {
      log('Error updating relapse: $e');
      return Left(
          ServerFailure('Une erreur s\'est produite : ${e.toString()}'));
    }
  }
}
