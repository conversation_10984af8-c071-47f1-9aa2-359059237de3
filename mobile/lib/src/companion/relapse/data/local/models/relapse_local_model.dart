import 'package:objectbox/objectbox.dart';
import 'package:s3g/src/sync/sync_service.dart' show SyncStatus;
import 'package:uuid/uuid.dart';
import 'package:s3g/src/manager/instance_features/relapse/domain/entity/relapse.dart';

@Entity()
class CompanionRelapseLocalModel {
  @Id()
  int oid = 0;

  String? id;
  String localId;
  String companionId;
  String description;

  // Sync metadata
  String syncStatus;
  DateTime? lastSyncAttempt;
  String? syncError;
  bool isDeleted;

  // Timestamps
  DateTime createdAt;
  DateTime updatedAt;

  CompanionRelapseLocalModel({
    this.id,
    required this.localId,
    required this.companionId,
    required this.description,
    required this.syncStatus,
    this.lastSyncAttempt,
    this.syncError,
    this.isDeleted = false,
    required this.createdAt,
    required this.updatedAt,
  });

  // Factory method to create from entity
  factory CompanionRelapseLocalModel.fromEntity(
      Relapse relapse, String companionId) {
    return CompanionRelapseLocalModel(
      id: relapse.id,
      localId: relapse.id,
      companionId: companionId,
      description: relapse.description,
      syncStatus: SyncStatus.synced.value,
      isDeleted: false,
      createdAt: relapse.createdAt,
      updatedAt: relapse.updatedAt,
    );
  }

  // Factory method to create offline instance
  factory CompanionRelapseLocalModel.createOffline({
    required String companionId,
    required String description,
  }) {
    final now = DateTime.now();
    return CompanionRelapseLocalModel(
      localId: const Uuid().v4(),
      companionId: companionId,
      description: description,
      syncStatus: SyncStatus.pending.value,
      isDeleted: false,
      createdAt: now,
      updatedAt: now,
    );
  }

  // Convert to entity
  Relapse toEntity() {
    return Relapse(
      id: id ?? localId,
      description: description,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  // Clone with updates
  CompanionRelapseLocalModel copyWith({
    String? id,
    String? description,
    String? syncStatus,
    DateTime? lastSyncAttempt,
    String? syncError,
    bool? isDeleted,
    DateTime? updatedAt,
  }) {
    return CompanionRelapseLocalModel(
      id: id ?? this.id,
      localId: localId,
      companionId: companionId,
      description: description ?? this.description,
      syncStatus: syncStatus ?? this.syncStatus,
      lastSyncAttempt: lastSyncAttempt ?? this.lastSyncAttempt,
      syncError: syncError ?? this.syncError,
      isDeleted: isDeleted ?? this.isDeleted,
      createdAt: createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Check if needs sync
  bool get needsSync =>
      SyncStatus.fromString(syncStatus) == SyncStatus.pending ||
      SyncStatus.fromString(syncStatus) == SyncStatus.failed;
}
