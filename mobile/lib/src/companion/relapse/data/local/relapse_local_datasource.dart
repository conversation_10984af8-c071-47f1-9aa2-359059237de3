import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/objectbox/objectbox.dart';
import 'package:s3g/src/sync/sync_service.dart' show SyncStatus;
import 'models/relapse_local_model.dart';

abstract class RelapseLocalDataSource {
  Future<void> saveRelapse(CompanionRelapseLocalModel relapse);
  Future<void> saveRelapses(List<CompanionRelapseLocalModel> relapses);
  Future<CompanionRelapseLocalModel?> getRelapseById(String id);
  Future<CompanionRelapseLocalModel?> getRelapseByLocalId(String localId);
  Future<CompanionRelapseLocalModel?> getRelapseByCompanionId(
      String companionId);
  Future<List<CompanionRelapseLocalModel>> getAllRelapses();
  Future<List<CompanionRelapseLocalModel>> getPendingSyncRelapses();
  Future<void> updateRelapseFromServer(CompanionRelapseLocalModel relapse);
  Future<void> deleteRelapse(String localId);
  Future<void> deleteRelapseById(String id);
  Future<void> deleteRelapseByCompanionId(String companionId);
  Future<void> markRelapseAsDeleted(String localId);
  Future<void> markRelapseAsDeletedById(String id);
  Future<void> markRelapseAsSynced(String localId, String serverId);
  Future<void> markRelapseSyncFailed(String localId, String error);
  Future<void> updateRelapseContent(String localId, {String? description});
  Future<void> clearAll();
}

@Injectable(as: RelapseLocalDataSource)
class RelapseLocalDataSourceImpl implements RelapseLocalDataSource {
  final ObjectBox _objectBox;

  late final Box<CompanionRelapseLocalModel> _box;

  RelapseLocalDataSourceImpl(this._objectBox) {
    _box = _objectBox.store.box<CompanionRelapseLocalModel>();
  }

  @override
  Future<void> saveRelapse(CompanionRelapseLocalModel relapse) async {
    try {
      CompanionRelapseLocalModel? existing;

      // First, check if a relapse with this server ID already exists
      if (relapse.id != null) {
        existing = await getRelapseById(relapse.id!);
      }

      // If not found by server ID, check by localId
      existing ??= await getRelapseByLocalId(relapse.localId);

      if (existing != null) {
        // Update the existing record, preserving the ObjectBox ID
        existing.id = relapse.id; // Update server ID if it was assigned
        existing.description = relapse.description;
        existing.companionId = relapse.companionId;
        existing.createdAt = relapse.createdAt;
        existing.updatedAt = relapse.updatedAt;
        existing.isDeleted = relapse.isDeleted;
        existing.syncStatus = relapse.syncStatus;
        existing.syncError = relapse.syncError;
        existing.lastSyncAttempt = relapse.lastSyncAttempt;

        await _box.putAsync(existing);
        log('Updated existing relapse with id: ${relapse.id}');
        return;
      }

      // No existing record found, save as new
      await _box.putAsync(relapse);
      log('Saved relapse with localId: ${relapse.localId}');
    } catch (e) {
      log('Error saving relapse: $e');
      rethrow;
    }
  }

  @override
  Future<void> saveRelapses(List<CompanionRelapseLocalModel> relapses) async {
    try {
      // Process each relapse individually to handle updates
      for (final relapse in relapses) {
        await saveRelapse(relapse);
      }
      log('Saved ${relapses.length} relapses');
    } catch (e) {
      log('Error saving relapses: $e');
      rethrow;
    }
  }

  @override
  Future<CompanionRelapseLocalModel?> getRelapseById(String id) async {
    try {
      final query =
          _box.query(CompanionRelapseLocalModel_.id.equals(id)).build();

      final result = await query.findFirstAsync();
      query.close();

      return result;
    } catch (e) {
      log('Error getting relapse by id: $e');
      return null;
    }
  }

  @override
  Future<CompanionRelapseLocalModel?> getRelapseByLocalId(
      String localId) async {
    try {
      final query = _box
          .query(CompanionRelapseLocalModel_.localId.equals(localId))
          .build();
      final result = query.findFirst();
      query.close();
      return result;
    } catch (e) {
      log('Error getting relapse by localId: $e');
      return null;
    }
  }

  @override
  Future<CompanionRelapseLocalModel?> getRelapseByCompanionId(
      String companionId) async {
    try {
      final query = _box
          .query(CompanionRelapseLocalModel_.companionId.equals(companionId) &
              CompanionRelapseLocalModel_.isDeleted.equals(false))
          .build();
      final result = query.findFirst();
      query.close();
      return result;
    } catch (e) {
      log('Error getting relapse by companionId: $e');
      return null;
    }
  }

  @override
  Future<List<CompanionRelapseLocalModel>> getAllRelapses() async {
    try {
      final query = _box
          .query(CompanionRelapseLocalModel_.isDeleted.equals(false))
          .build();
      final results = query.find();
      query.close();
      return results;
    } catch (e) {
      log('Error getting all relapses: $e');
      return [];
    }
  }

  @override
  Future<List<CompanionRelapseLocalModel>> getPendingSyncRelapses() async {
    try {
      final query = _box
          .query(
            CompanionRelapseLocalModel_.syncStatus
                    .equals(SyncStatus.pending.value) |
                CompanionRelapseLocalModel_.syncStatus
                    .equals(SyncStatus.failed.value),
          )
          .build();
      final results = query.find();
      query.close();
      return results;
    } catch (e) {
      log('Error getting pending sync relapses: $e');
      return [];
    }
  }

  @override
  Future<void> updateRelapseFromServer(
      CompanionRelapseLocalModel relapse) async {
    try {
      // Find existing relapse by server ID
      final existing = await getRelapseById(relapse.id!);
      if (existing != null) {
        // Check if local relapse has pending changes
        if (existing.needsSync) {
          // Local changes take priority - don't overwrite
          log('Relapse ${existing.id} has pending local changes, skipping server update');
          return;
        }

        // Only update if server version is newer
        if (relapse.updatedAt.isAfter(existing.updatedAt)) {
          // Update existing relapse, preserving local-only fields
          relapse.oid = existing.oid;
          relapse.localId = existing.localId;
          relapse.syncStatus = SyncStatus.synced.value;
          await _box.putAsync(relapse);
          log('Updated relapse from server: ${relapse.id}');
        }
      } else {
        // Check if we have any relapse with the same ID (including deleted ones)
        final query = _box
            .query(CompanionRelapseLocalModel_.id.equals(relapse.id!))
            .build();
        final conflictingRelapse = query.findFirst();
        query.close();

        if (conflictingRelapse != null) {
          // Check if local relapse has pending changes
          if (conflictingRelapse.needsSync) {
            // Local changes take priority - don't overwrite
            log('Relapse ${conflictingRelapse.id} has pending local changes, skipping server update');
            return;
          }

          // Only update if server version is newer
          if (relapse.updatedAt.isAfter(conflictingRelapse.updatedAt)) {
            // Update the existing relapse
            relapse.oid = conflictingRelapse.oid;
            relapse.localId = conflictingRelapse.localId;
            relapse.syncStatus = SyncStatus.synced.value;
            relapse.isDeleted = false; // Resurrect if it was deleted
            await _box.putAsync(relapse);
            log('Updated relapse from server: ${relapse.id}');
          }
        } else {
          // New relapse from server
          relapse.syncStatus = SyncStatus.synced.value;
          await _box.putAsync(relapse);
          log('Added new relapse from server: ${relapse.id}');
        }
      }
    } catch (e) {
      log('Error updating relapse from server: $e');
      rethrow;
    }
  }

  @override
  Future<void> deleteRelapse(String localId) async {
    try {
      final query = _box
          .query(CompanionRelapseLocalModel_.localId.equals(localId))
          .build();
      final relapse = query.findFirst();
      query.close();

      if (relapse != null) {
        await _box.removeAsync(relapse.oid);
        log('Deleted relapse with localId: $localId');
      }
    } catch (e) {
      log('Error deleting relapse: $e');
      rethrow;
    }
  }

  @override
  Future<void> deleteRelapseById(String id) async {
    try {
      final query =
          _box.query(CompanionRelapseLocalModel_.id.equals(id)).build();
      final relapse = await query.findFirstAsync();
      query.close();

      if (relapse != null) {
        await _box.removeAsync(relapse.oid);
        log('Deleted relapse with id: $id');
      }
    } catch (e) {
      log('Error deleting relapse by id: $e');
      rethrow;
    }
  }

  @override
  Future<void> deleteRelapseByCompanionId(String companionId) async {
    try {
      final query = _box
          .query(CompanionRelapseLocalModel_.companionId.equals(companionId))
          .build();
      final relapse = await query.findFirstAsync();
      query.close();

      if (relapse != null) {
        await _box.removeAsync(relapse.oid);
        log('Deleted relapse with companionId: $companionId');
      }
    } catch (e) {
      log('Error deleting relapse by companionId: $e');
      rethrow;
    }
  }

  @override
  Future<void> markRelapseAsDeleted(String localId) async {
    try {
      final relapse = await getRelapseByLocalId(localId);
      if (relapse != null) {
        relapse.isDeleted = true;
        relapse.syncStatus = SyncStatus.pending.value;
        relapse.updatedAt = DateTime.now();
        await _box.putAsync(relapse);
        log('Marked relapse as deleted: $localId');
      }
    } catch (e) {
      log('Error marking relapse as deleted: $e');
      rethrow;
    }
  }

  @override
  Future<void> markRelapseAsDeletedById(String id) async {
    try {
      final relapse = await getRelapseById(id);
      if (relapse != null) {
        relapse.isDeleted = true;
        relapse.syncStatus = SyncStatus.pending.value;
        relapse.updatedAt = DateTime.now();
        await _box.putAsync(relapse);
        log('Marked relapse as deleted by id: $id');
      }
    } catch (e) {
      log('Error marking relapse as deleted by id: $e');
      rethrow;
    }
  }

  @override
  Future<void> markRelapseAsSynced(String localId, String serverId) async {
    try {
      final relapse = await getRelapseByLocalId(localId);
      if (relapse != null) {
        relapse.markAsSynced(serverId);
        await saveRelapse(relapse);
        log('Marked relapse as synced: $localId -> $serverId');
      }
    } catch (e) {
      log('Error marking relapse as synced: $e');
      rethrow;
    }
  }

  @override
  Future<void> markRelapseSyncFailed(String localId, String error) async {
    try {
      final relapse = await getRelapseByLocalId(localId);
      if (relapse != null) {
        relapse.updateSyncStatus(SyncStatus.failed, error: error);
        await saveRelapse(relapse);
        log('Marked relapse sync as failed: $localId');
      }
    } catch (e) {
      log('Error marking relapse sync as failed: $e');
      rethrow;
    }
  }

  @override
  Future<void> updateRelapseContent(String localId,
      {String? description}) async {
    try {
      final relapse = await getRelapseByLocalId(localId);
      if (relapse != null) {
        relapse.updateContent(newDescription: description);
        await saveRelapse(relapse);
        log('Updated relapse content: $localId');
      }
    } catch (e) {
      log('Error updating relapse content: $e');
      rethrow;
    }
  }

  @override
  Future<void> clearAll() async {
    try {
      _box.removeAll();
      log('Cleared all relapses from local storage');
    } catch (e) {
      log('Error clearing relapses: $e');
      rethrow;
    }
  }
}
