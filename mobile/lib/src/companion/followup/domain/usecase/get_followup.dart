import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/companion/followup/domain/entity/followup.dart';

import '../repository/followup_repository.dart';

@injectable
class GetFollowup extends UseCase<Followup, GetFollowupParams> {
  final FollowupRepository repository;

  GetFollowup({required this.repository});

  @override
  call(GetFollowupParams params) {
    return repository.getFollowup(
      companionId: params.companionId,
      followupId: params.followupId,
    );
  }
}

class GetFollowupParams {
  final String companionId;
  final String followupId;

  GetFollowupParams({
    required this.companionId,
    required this.followupId,
  });
}
