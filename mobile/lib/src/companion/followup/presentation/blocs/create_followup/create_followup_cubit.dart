import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';
import 'package:s3g/core/network/connection_checker.dart';
import 'package:s3g/src/companion/followup/domain/usecase/create_followup.dart';
import 'package:s3g/src/companion/followup/followup.dart';

part 'create_followup_state.dart';

@injectable
class CreateFollowupCubit extends Cubit<CreateFollowupState> {
  final CreateFollowup _createFollowup;
  final ConnectionChecker _connectionChecker;

  CreateFollowupCubit(
    this._createFollowup,
    this._connectionChecker,
  ) : super(CreateFollowupInitial());

  Future<void> createFollowup(CreateFollowupParams params) async {
    emit(CreateFollowupLoading());

    // Check if device is online
    final isOnline = await _connectionChecker.isOnline();

    final result = await _createFollowup(params);

    result.fold(
      (l) => emit(CreateFollowupFailure(message: l.message)),
      (r) => emit(CreateFollowupSuccess(
        followup: r,
        isOffline: !isOnline,
      )),
    );
  }
}
