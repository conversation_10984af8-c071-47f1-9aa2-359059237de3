part of 'create_followup_cubit.dart';

@immutable
sealed class CreateFollowupState {}

final class CreateFollowupInitial extends CreateFollowupState {}

final class CreateFollowupLoading extends CreateFollowupState {}

final class CreateFollowupSuccess extends CreateFollowupState {
  final Followup followup;
  final bool isOffline;

  CreateFollowupSuccess({
    required this.followup,
    this.isOffline = false,
  });
}

final class CreateFollowupFailure extends CreateFollowupState {
  final String message;

  CreateFollowupFailure({required this.message});
}
