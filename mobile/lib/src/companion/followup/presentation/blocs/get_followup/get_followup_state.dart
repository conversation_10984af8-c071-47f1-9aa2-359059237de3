part of 'get_followup_cubit.dart';

@immutable
sealed class GetFollowupState {}

final class GetFollowupInitial extends GetFollowupState {}

final class GetFollowupLoading extends GetFollowupState {}

final class GetFollowupSuccess extends GetFollowupState {
  final Followup followup;

  GetFollowupSuccess(this.followup);
}

final class GetFollowupFailure extends GetFollowupState {
  final String message;

  GetFollowupFailure({required this.message});
}
