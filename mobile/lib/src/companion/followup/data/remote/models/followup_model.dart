// ignore_for_file: overridden_fields

import 'package:json_annotation/json_annotation.dart';
import 'package:s3g/src/companion/followup/domain/entity/followup.dart';

part 'followup_model.g.dart';

@JsonSerializable()
class FollowupModel extends Followup {
  @override
  @Json<PERSON>ey(name: 'created_at')
  final DateTime createdAt;

  @override
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  const FollowupModel({
    required super.id,
    required super.title,
    required super.description,
    required this.createdAt,
    required this.updatedAt,
  }) : super(createdAt: createdAt, updatedAt: updatedAt);

  factory FollowupModel.fromJson(Map<String, dynamic> json) =>
      _$FollowupModelFromJson(json);

  Map<String, dynamic> toJson() => _$FollowupModelToJson(this);
}
