import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:getwidget/getwidget.dart';
import 'package:go_router/go_router.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/companion/companion/companion.dart';

class CompanionScaffoldWrapper extends StatefulWidget {
  final String companionId;

  const CompanionScaffoldWrapper({
    Key? key,
    required this.navigationShell,
    required this.companionId,
  }) : super(key: key ?? const ValueKey<String>('CompanionScaffoldWrapper'));

  final StatefulNavigationShell navigationShell;

  @override
  State<CompanionScaffoldWrapper> createState() =>
      _CompanionScaffoldWrapperState();
}

class _CompanionScaffoldWrapperState extends State<CompanionScaffoldWrapper> {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      key: ValueKey(widget.companionId),
      create: (context) {
        return CompanionShowCubit(
          widget.companionId,
          getCompanionUseCase: getIt(),
        )..getCompanion();
      },
      child: _CompanionScaffold(
        navigationShell: widget.navigationShell,
        companionId: widget.companionId,
      ),
    );
  }
}

class _CompanionScaffold extends StatelessWidget {
  const _CompanionScaffold({
    required this.navigationShell,
    required this.companionId,
  });

  final StatefulNavigationShell navigationShell;

  final String companionId;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: BlocBuilder<CompanionShowCubit, CompanionShowState>(
          builder: (context, state) {
            switch (state) {
              case CompanionShowLoaded():
                return Text(state.companion.instance.code);
              default:
            }

            return const SizedBox.shrink();
          },
        ),
      ),
      body: BlocBuilder<CompanionShowCubit, CompanionShowState>(
        builder: (context, state) {
          switch (state) {
            case CompanionShowInitial() || CompanionShowLoading():
              return const Center(
                child: GFLoader(size: GFSize.LARGE),
              );

            case CompanionShowLoaded():
              return SafeArea(child: navigationShell);

            case CompanionShowError(message: String message):
              return RetryWidget(
                message: message,
                onPressed: () {
                  context.read<CompanionShowCubit>().getCompanion();
                },
              );
            // ignore: unreachable_switch_default
            default:
              break;
          }

          return const SizedBox.shrink();
        },
      ),
      bottomNavigationBar: BlocBuilder<CompanionShowCubit, CompanionShowState>(
        builder: (context, state) {
          switch (state) {
            case CompanionShowLoaded(companion: Companion companion):
              return _BottomNavigationBar(
                navigationShell: navigationShell,
                companion: companion,
              );
            default:
          }
          return _BottomNavigationBar(
            navigationShell: navigationShell,
            companion: null,
          );
        },
      ),
    );
  }
}

class _BottomNavigationBar extends StatelessWidget {
  final Companion? companion;
  final StatefulNavigationShell navigationShell;

  const _BottomNavigationBar({
    required this.navigationShell,
    required this.companion,
  });

  void _goBranch(int index) {
    navigationShell.goBranch(
      index,
      initialLocation: index == navigationShell.currentIndex,
    );
  }

  @override
  Widget build(BuildContext context) {
    final relapse = companion?.instance.relapse;

    return NavigationBar(
      labelBehavior: NavigationDestinationLabelBehavior.alwaysShow,
      selectedIndex: navigationShell.currentIndex,
      indicatorColor: Theme.of(context).colorScheme.primary,
      onDestinationSelected: (int index) => _goBranch(index),
      destinations: <Widget>[
        const NavigationDestination(
          selectedIcon: Icon(Icons.info_outline, color: Colors.white),
          icon: Icon(Icons.info_outline),
          label: 'Détail',
        ),
        const NavigationDestination(
          selectedIcon: Icon(Icons.track_changes_outlined, color: Colors.white),
          icon: Icon(Icons.track_changes_outlined),
          label: 'Suivis',
        ),
        NavigationDestination(
          selectedIcon: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Icon(Icons.trending_down_outlined, color: Colors.white),
              if (relapse != null) ...[
                const SizedBox(width: 5),
                Container(
                  width: 10,
                  height: 10,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.orange[300],
                  ),
                )
              ],
            ],
          ),
          icon: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Icon(Icons.trending_down_outlined),
              if (relapse != null) ...[
                const SizedBox(width: 5),
                Container(
                  width: 10,
                  height: 10,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.orange[300],
                  ),
                )
              ],
            ],
          ),
          label: 'Rechute',
        ),
      ],
    );
  }
}
