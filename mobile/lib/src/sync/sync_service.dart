import 'dart:async';
import 'dart:developer';

import 'package:injectable/injectable.dart';
import 'package:s3g/core/network/connection_checker.dart';
import 'package:s3g/src/sync/handlers/sync_handlers.dart';

import 'status.dart';

export 'status.dart';

abstract class SyncService {
  Future<void> syncAll();
  Future<void> syncManagerInstances();
  Future<void> syncCompanionFollowups();
  Future<void> syncCompanionRelapses();
  Future<void> syncManagerCompanions();
  Future<bool> isOnline();
  Stream<SyncStatus> get syncStatusStream;
  void startPeriodicSync();
  void stopPeriodicSync();
}

@LazySingleton(as: SyncService)
class SyncServiceImpl implements SyncService {
  final ConnectionChecker _connectionChecker;
  final InstanceSyncHandler _instanceSyncHandler;
  final CompanionFollowupSyncHandler _companionFollowupSyncHandler;
  final CompanionRelapseSyncHandler _companionRelapseSyncHandler;
  final ManagerCompanionSyncHandler _managerCompanionSyncHandler;

  final StreamController<SyncStatus> _syncStatusController =
      StreamController<SyncStatus>.broadcast();

  Timer? _periodicSyncTimer;
  bool _isSyncing = false;

  SyncServiceImpl({
    required ConnectionChecker connectionChecker,
    required InstanceSyncHandler instanceSyncHandler,
    required CompanionFollowupSyncHandler companionFollowupSyncHandler,
    required CompanionRelapseSyncHandler companionRelapseSyncHandler,
    required ManagerCompanionSyncHandler managerCompanionSyncHandler,
  })  : _connectionChecker = connectionChecker,
        _instanceSyncHandler = instanceSyncHandler,
        _companionFollowupSyncHandler = companionFollowupSyncHandler,
        _companionRelapseSyncHandler = companionRelapseSyncHandler,
        _managerCompanionSyncHandler = managerCompanionSyncHandler;

  @override
  Stream<SyncStatus> get syncStatusStream => _syncStatusController.stream;

  @override
  Future<bool> isOnline() async {
    return await _connectionChecker.isOnline();
  }

  @override
  Future<void> syncAll() async {
    if (_isSyncing) {
      log('Sync already in progress, skipping...');
      return;
    }

    if (!await isOnline()) {
      log('Device is offline, skipping sync');
      _syncStatusController.add(SyncStatus.failed);
      return;
    }

    _isSyncing = true;
    _syncStatusController.add(SyncStatus.syncing);

    try {
      await syncManagerInstances();
      await syncManagerCompanions();
      await syncCompanionFollowups();
      await syncCompanionRelapses();
      _syncStatusController.add(SyncStatus.synced);
      log('Sync completed successfully');
    } catch (e) {
      log('Sync failed: $e');
      _syncStatusController.add(SyncStatus.failed);
    } finally {
      _isSyncing = false;
    }
  }

  @override
  Future<void> syncManagerInstances() async {
    await _instanceSyncHandler.syncAll();
  }

  @override
  Future<void> syncCompanionFollowups() async {
    await _companionFollowupSyncHandler.syncAll();
  }

  @override
  Future<void> syncCompanionRelapses() async {
    await _companionRelapseSyncHandler.syncAll();
  }

  @override
  Future<void> syncManagerCompanions() async {
    await _managerCompanionSyncHandler.syncAll();
  }

  @override
  void startPeriodicSync() {
    stopPeriodicSync(); // Stop any existing timer

    _periodicSyncTimer = Timer.periodic(
      const Duration(minutes: 5), // Sync every 5 minutes
      (_) => syncAll(),
    );

    log('Periodic sync started');
  }

  @override
  void stopPeriodicSync() {
    _periodicSyncTimer?.cancel();
    _periodicSyncTimer = null;
    log('Periodic sync stopped');
  }

  void dispose() {
    stopPeriodicSync();
    _syncStatusController.close();
  }
}
