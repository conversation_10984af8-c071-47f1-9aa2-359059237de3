import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/src/sync/handlers/base_sync_handler.dart';
import 'package:s3g/src/manager/instance/data/local/instance_local_datasource.dart';
import 'package:s3g/src/manager/instance/data/local/models/instance_local_model.dart';
import 'package:s3g/src/manager/instance/data/remote/instance_remote_datasource.dart';
import 'package:s3g/src/manager/instance/domain/entity/instance.dart';
import 'package:s3g/src/sync/sync_service.dart' show SyncStatus;

@injectable
class InstanceSyncHandler extends BaseSyncHandler {
  final InstanceLocalDataSource _localDataSource;
  final InstanceRemoteDataSource _remoteDataSource;

  InstanceSyncHandler({
    required InstanceLocalDataSource localDataSource,
    required InstanceRemoteDataSource remoteDataSource,
  })  : _localDataSource = localDataSource,
        _remoteDataSource = remoteDataSource;

  @override
  String get featureName => 'Instance';

  @override
  Future<void> syncAll() async {
    log('Starting $featureName sync...');

    try {
      // Get all pending instances
      final pendingInstances = await _localDataSource.getPendingSyncInstances();

      for (final localInstance in pendingInstances) {
        await _syncSingleInstance(localInstance);
      }

      log('$featureName sync completed');
    } catch (e) {
      log('$featureName sync failed: $e');
      rethrow;
    }
  }

  Future<void> _syncSingleInstance(InstanceLocalModel localInstance) async {
    try {
      if (localInstance.isDeleted) {
        // Handle deletion
        if (localInstance.id != null) {
          // Delete from server if it exists there
          log('Deleting instance ${localInstance.id} from server');

          try {
            await _remoteDataSource.deleteInstance(
              healthCenterId: localInstance.healthCenterId,
              instanceId: localInstance.id!,
            );
          } on DioException catch (e) {
            if (e.response?.statusCode != 404) {
              rethrow;
            }
          } catch (e) {
            rethrow;
          }
        }
        // Remove from local storage
        await _localDataSource.deleteInstance(localInstance.localId);
        return;
      }

      if (localInstance.isOfflineCreated) {
        // Create new instance on server
        log('Creating new instance on server: ${localInstance.code}');

        final result = await _remoteDataSource.createInstance(
          healthCenterId: localInstance.healthCenterId,
          survivorCode: localInstance.survivorCode,
          type: InstanceType.values
              .firstWhere((t) => t.name == localInstance.type),
          description: localInstance.description,
        );

        // Mark as synced with server ID
        await _localDataSource.markInstanceAsSynced(
          localInstance.localId,
          result.id,
        );

        log('Instance created successfully with ID: ${result.id}');
      } else {
        // Update existing instance on server
        log('Updating instance on server: ${localInstance.id}');

        await _remoteDataSource.editInstance(
          healthCenterId: localInstance.healthCenterId,
          instanceId: localInstance.id!,
          status: InstanceStatus.values
              .firstWhere((s) => s.name == localInstance.status),
          description: localInstance.description,
        );

        // Mark as synced
        localInstance.updateSyncStatus(SyncStatus.synced);
        await _localDataSource.saveInstance(localInstance);

        log('Instance updated successfully');
      }
    } catch (e) {
      log('Failed to sync instance ${localInstance.localId}: $e');
      await _localDataSource.markInstanceSyncFailed(
        localInstance.localId,
        e.toString(),
      );
    }
  }
}
