import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/src/sync/handlers/base_sync_handler.dart';
import 'package:s3g/src/companion/relapse/data/local/models/relapse_local_model.dart';
import 'package:s3g/src/companion/relapse/data/local/relapse_local_datasource.dart';
import 'package:s3g/src/companion/relapse/data/remote/relapse_remote_datasource.dart';

import '../sync_service.dart';

@injectable
class CompanionRelapseSyncHandler extends BaseSyncHandler {
  final RelapseLocalDataSource _localDataSource;
  final RelapseRemoteDataSource _remoteDataSource;

  CompanionRelapseSyncHandler({
    required RelapseLocalDataSource localDataSource,
    required RelapseRemoteDataSource remoteDataSource,
  })  : _localDataSource = localDataSource,
        _remoteDataSource = remoteDataSource;

  @override
  String get featureName => 'Companion Relapse';

  @override
  Future<void> syncAll() async {
    log('Starting $featureName sync...');

    try {
      // Get all pending relapses
      final pendingRelapses = await _localDataSource.getPendingSyncRelapses();

      for (final localRelapse in pendingRelapses) {
        await _syncSingleRelapse(localRelapse);
      }

      log('$featureName sync completed');
    } catch (e) {
      log('$featureName sync failed: $e');
      rethrow;
    }
  }

  Future<void> _syncSingleRelapse(
    CompanionRelapseLocalModel localRelapse,
  ) async {
    try {
      if (localRelapse.isDeleted) {
        // Handle deletion
        if (localRelapse.id != null) {
          // Delete from server if it exists there
          log('Deleting relapse for companion ${localRelapse.companionId} from server');
          try {
            await _remoteDataSource.deleteRelapse(
              companionId: localRelapse.companionId,
            );
          } on DioException catch (e) {
            if (e.response?.statusCode != 404) {
              rethrow;
            }
          } catch (e) {
            rethrow;
          }
        }
        // Remove from local storage
        await _localDataSource.deleteRelapse(localRelapse.localId);
        return;
      }

      if (localRelapse.isOfflineCreated) {
        // Create new relapse on server
        log('Creating new relapse on server for companion: ${localRelapse.companionId}');

        final result = await _remoteDataSource.createRelapse(
          companionId: localRelapse.companionId,
          description: localRelapse.description,
        );

        // Use helper method to mark as synced
        await _localDataSource.markRelapseAsSynced(
          localRelapse.localId,
          result.id,
        );

        log('Relapse created successfully with ID: ${result.id}');
      } else {
        // Update existing relapse on server
        log('Updating relapse on server for companion: ${localRelapse.companionId}');

        await _remoteDataSource.updateRelapse(
          companionId: localRelapse.companionId,
          description: localRelapse.description,
        );

        // Use helper method to mark as synced
        localRelapse.updateSyncStatus(SyncStatus.synced);
        await _localDataSource.saveRelapse(localRelapse);

        log('Relapse updated successfully');
      }
    } catch (e) {
      log('Failed to sync relapse for companion ${localRelapse.companionId}: $e');

      // Use helper method to mark sync as failed
      await _localDataSource.markRelapseSyncFailed(
        localRelapse.localId,
        e.toString(),
      );
    }
  }
}
