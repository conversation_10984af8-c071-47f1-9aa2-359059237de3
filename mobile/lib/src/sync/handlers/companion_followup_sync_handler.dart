import 'dart:developer';

import 'package:dio/dio.dart' show DioException;
import 'package:injectable/injectable.dart';
import 'package:s3g/src/sync/handlers/base_sync_handler.dart';
import 'package:s3g/src/companion/followup/data/local/followup_local_datasource.dart';
import 'package:s3g/src/companion/followup/data/local/models/followup_local_model.dart';
import 'package:s3g/src/companion/followup/data/remote/followup_remote_datasource.dart';
import 'package:s3g/src/sync/sync_service.dart' show SyncStatus;

@injectable
class CompanionFollowupSyncHandler extends BaseSyncHandler {
  final FollowupLocalDataSource _localDataSource;
  final FollowupRemoteDataSource _remoteDataSource;

  CompanionFollowupSyncHandler({
    required FollowupLocalDataSource localDataSource,
    required FollowupRemoteDataSource remoteDataSource,
  })  : _localDataSource = localDataSource,
        _remoteDataSource = remoteDataSource;

  @override
  String get featureName => 'Companion Followup';

  @override
  Future<void> syncAll() async {
    log('Starting $featureName sync...');

    try {
      // Get all pending followups
      final pendingFollowups = await _localDataSource.getPendingSyncFollowups();

      for (final localFollowup in pendingFollowups) {
        await _syncSingleFollowup(localFollowup);
      }

      log('$featureName sync completed');
    } catch (e) {
      log('$featureName sync failed: $e');
      rethrow;
    }
  }

  Future<void> _syncSingleFollowup(FollowupLocalModel localFollowup) async {
    try {
      if (localFollowup.isDeleted) {
        // Handle deletion
        if (localFollowup.id != null) {
          // Delete from server if it exists there
          log('Deleting followup ${localFollowup.id} from server');
          try {
            await _remoteDataSource.deleteFollowup(
              companionId: localFollowup.companionId,
              followupId: localFollowup.id!,
            );
          } on DioException catch (e) {
            if (e.response?.statusCode != 404) {
              rethrow;
            }
          } catch (e) {
            rethrow;
          }
        }

        // Remove from local storage
        await _localDataSource.deleteFollowup(localFollowup.localId);
        return;
      }

      if (localFollowup.isOfflineCreated) {
        // Create new followup on server
        log('Creating new followup on server: ${localFollowup.title}');

        final result = await _remoteDataSource.createFollowup(
          companionId: localFollowup.companionId,
          title: localFollowup.title,
          description: localFollowup.description,
        );

        // Mark as synced with server ID
        await _localDataSource.markFollowupAsSynced(
          localFollowup.localId,
          result.id,
        );

        log('Followup created successfully with ID: ${result.id}');
      } else {
        // Update existing followup on server
        log('Updating followup on server: ${localFollowup.id}');

        await _remoteDataSource.updateFollowup(
          companionId: localFollowup.companionId,
          followupId: localFollowup.id!,
          title: localFollowup.title,
          description: localFollowup.description,
        );

        // Mark as synced
        localFollowup.updateSyncStatus(SyncStatus.synced);
        await _localDataSource.saveFollowup(localFollowup);

        log('Followup updated successfully');
      }
    } catch (e) {
      log('Failed to sync followup ${localFollowup.localId}: $e');
      await _localDataSource.markFollowupSyncFailed(
        localFollowup.localId,
        e.toString(),
      );
    }
  }
}
