import 'dart:developer';

import 'package:injectable/injectable.dart';
import 'package:s3g/src/manager/instance/data/local/instance_local_datasource.dart';
import 'package:s3g/src/manager/instance_features/companion/data/local/instance_companion_local_datasource.dart';
import 'package:s3g/src/manager/instance_features/companion/data/remote/companion_remote_datasource.dart';
import 'package:s3g/src/manager/member/member.dart';
import 'package:s3g/src/sync/handlers/base_sync_handler.dart';
import 'package:s3g/src/sync/sync_service.dart';

@injectable
class ManagerCompanionSyncHandler extends BaseSyncHandler {
  final InstanceCompanionLocalDataSource _localDataSource;
  final CompanionRemoteDataSource _remoteDataSource;
  final InstanceLocalDataSource _instanceLocalDataSource;

  @override
  String get featureName => 'Manager Companion';

  ManagerCompanionSyncHandler({
    required InstanceCompanionLocalDataSource localDataSource,
    required CompanionRemoteDataSource remoteDataSource,
    required InstanceLocalDataSource instanceLocalDataSource,
  })  : _localDataSource = localDataSource,
        _remoteDataSource = remoteDataSource,
        _instanceLocalDataSource = instanceLocalDataSource;

  @override
  Future<void> syncAll() async {
    final pendingCompanions = await _localDataSource.getPendingSyncCompanions();
    log('Found ${pendingCompanions.length} companions to sync');

    for (final companion in pendingCompanions) {
      try {
        // Get the instance to check if it's synced
        final instance = await _instanceLocalDataSource.getInstanceByLocalId(
          companion.instanceLocalId,
        );

        if (instance == null) {
          log('Instance not found for companion ${companion.localId}');
          continue;
        }

        // Skip if instance is not synced yet
        if (instance.syncStatus != SyncStatus.synced.value) {
          log('Instance ${instance.localId} not synced yet (status: ${instance.syncStatus}), skipping companion ${companion.localId}');
          continue;
        }

        // Update sync status to syncing
        companion.updateSyncStatus(SyncStatus.syncing);
        await _localDataSource.updateCompanion(companion);

        if (companion.isDeleted) {
          // Handle deletion
          if (companion.id != null) {
            await _remoteDataSource.deleteCompanion(
              instanceId: instance.id!,
              companionId: companion.id!,
            );
          }
          // Remove from local after successful sync
          await _localDataSource.deleteCompanionByLocalId(companion.localId);
        } else if (companion.isOfflineCreated) {
          // Create on server
          final response = await _remoteDataSource.createCompanion(
            instanceId: instance.id!,
            type: MemberCompanionRole.values.firstWhere(
              (e) => e.name == companion.type,
            ),
            userId: companion.toEntity().user.id,
          );

          // Update local with server ID
          companion.markAsSynced(response.id);
          await _localDataSource.updateCompanion(companion);
        } else {
          // Update on server
          // Note: There's no update endpoint for companion, so we'll just mark as synced
          companion.updateSyncStatus(SyncStatus.synced);
          await _localDataSource.updateCompanion(companion);
        }

        log('Successfully synced companion ${companion.localId}');
      } catch (e) {
        log('Failed to sync companion ${companion.localId}: $e');
        companion.updateSyncStatus(SyncStatus.failed, error: e.toString());
        await _localDataSource.updateCompanion(companion);
      }
    }
  }
}
