import 'package:injectable/injectable.dart';
import 'package:s3g/core/objectbox/objectbox.dart';
import 'package:s3g/src/authentication/data/local/models/user_local_model.dart';
import 'package:s3g/src/authentication/domain/entities/user.dart';

abstract class AuthenticationLocalDataSource {
  Future<UserLocalModel?> getUser();

  Future<void> setUser(User user);

  Future<void> deleteUser();
}

@Injectable(as: AuthenticationLocalDataSource)
class AuthenticationLocalDataSourceImpl
    implements AuthenticationLocalDataSource {
  final ObjectBox objectBox;

  AuthenticationLocalDataSourceImpl({required this.objectBox});

  @override
  Future<UserLocalModel?> getUser() async {
    final box = objectBox.store.box<UserLocalModel>();
    final users = await box.getAllAsync();

    return users.firstOrNull;
  }

  @override
  Future<void> setUser(User user) async {
    final box = objectBox.store.box<UserLocalModel>();

    // First check if a user with this ID already exists
    final query = box.query(UserLocalModel_.id.equals(user.id)).build();
    final existingUser = await query.findFirstAsync();
    query.close();

    if (existingUser != null) {
      // Update the existing user
      existingUser.name = user.name;
      existingUser.role = user.role.name;
      existingUser.phone = user.phone;
      existingUser.email = user.email;
      existingUser.description = user.description;
      existingUser.createdAt = user.createdAt;
      existingUser.updatedAt = user.updatedAt;
      await box.putAsync(existingUser);
    } else {
      // Remove all existing users first (since we only want one user)
      await box.removeAllAsync();
      // Then add the new user
      await box.putAsync(UserLocalModel.fromEntity(user));
    }
  }

  @override
  Future<void> deleteUser() async {
    final box = objectBox.store.box<UserLocalModel>();
    await box.removeAllAsync();
  }
}
