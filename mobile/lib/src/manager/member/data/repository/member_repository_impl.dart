import 'package:injectable/injectable.dart';
import 'package:s3g/core/errors/failures.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/network/connection_checker.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/src/authentication/domain/entities/user.dart';
import 'package:s3g/src/manager/member/data/local/member_local_datasource.dart';
import 'package:s3g/src/manager/member/data/local/models/member_local_model.dart';
import 'package:s3g/src/manager/member/data/remote/member_remote_datasource.dart';
import 'package:s3g/src/manager/member/domain/entity/member.dart';
import 'package:fpdart/fpdart.dart';

import '../../domain/repository/member_repository.dart';

@Injectable(as: MemberRepository)
class MemberRepositoryImpl extends MemberRepository {
  final MemberRemoteDataSource _remoteDataSource;
  final MemberLocalDataSource _localDataSource;
  final ConnectionChecker _connectionChecker;

  MemberRepositoryImpl(
    this._remoteDataSource,
    this._localDataSource,
    this._connectionChecker,
  );

  @override
  RepositoryResponse<List<Member>> getMembers({
    required String healthCenterId,
    required MemberType type,
  }) async {
    final isOnline = await _connectionChecker.isOnline();

    if (isOnline) {
      final response = await requestHelper(() => _remoteDataSource.getMembers(
            healthCenterId: healthCenterId,
            type: type,
          ));

      if (response.isRight()) {
        final members = response.fold(
          (l) => <Member>[],
          (r) => r,
        );

        // Cache members locally
        final localMembers = members
            .map((member) => MemberLocalModel.fromEntity(
                  member,
                  healthCenterId: healthCenterId,
                ))
            .toList();

        await _localDataSource.saveMembers(
          localMembers,
          healthCenterId: healthCenterId,
        );
      }

      return response;
    } else {
      // Get from local storage
      final localMembers = await _localDataSource.getAllMembers(
        healthCenterId: healthCenterId,
      );

      // Filter by type if needed
      final filteredMembers = localMembers.where((member) {
        final memberEntity = member.toEntity();
        if (type == MemberType.APS) {
          return memberEntity.responsibility != MemberResponsibility.COMPANION;
        } else {
          return memberEntity.responsibility == MemberResponsibility.COMPANION;
        }
      }).toList();

      final members = filteredMembers.map((m) => m.toEntity()).toList();

      if (members.isEmpty) {
        return Left(CacheFailure('Aucun membre disponible hors ligne'));
      }

      return Right(members);
    }
  }

  @override
  RepositoryResponse<Member> createMember(
    String healthCenterId, {
    required String name,
    required String phone,
    required String? email,
    required UserRole role,
    required String? description,
    required MemberResponsibility responsibility,
    required MemberCompanionRole? companionRole,
  }) async {
    final response = await requestHelper(() {
      return _remoteDataSource.createMember(
        healthCenterId,
        name: name,
        phone: phone,
        email: email,
        role: role,
        responsibility: responsibility,
        description: description,
        companionRole: companionRole,
      );
    });

    if (response.isRight()) {
      final member = response.fold(
        (l) => throw Exception(),
        (r) => r,
      );

      // Cache the new member
      final localMember = MemberLocalModel.fromEntity(
        member,
        healthCenterId: healthCenterId,
      );

      await _localDataSource.saveMember(
        localMember,
        healthCenterId: healthCenterId,
      );
    }

    return response;
  }

  @override
  RepositoryResponse<MessageResponse> deleteMember({
    required String healthCenterId,
    required String memberId,
    required MemberType type,
  }) async {
    final response = await requestHelper(() => _remoteDataSource.deleteMember(
          healthCenterId: healthCenterId,
          memberId: memberId,
          type: type,
        ));

    if (response.isRight()) {
      // Remove from local cache
      final localMember = await _localDataSource.getMemberById(
        memberId,
        healthCenterId: healthCenterId,
      );
      if (localMember != null) {
        await _localDataSource.clearAll();
      }
    }

    return response;
  }

  @override
  RepositoryResponse<Member> updateMember(
    String healthCenterId, {
    required String memberId,
    required String name,
    required String phone,
    required String? email,
    required UserRole role,
    required String? description,
    required MemberResponsibility responsibility,
    required MemberCompanionRole? companionRole,
  }) async {
    final response = await requestHelper(() {
      return _remoteDataSource.updateMember(
        healthCenterId,
        memberId: memberId,
        name: name,
        phone: phone,
        email: email,
        role: role,
        responsibility: responsibility,
        description: description,
        companionRole: companionRole,
      );
    });

    if (response.isRight()) {
      final member = response.fold(
        (l) => throw Exception(),
        (r) => r,
      );

      // Update local cache
      final localMember = MemberLocalModel.fromEntity(
        member,
        healthCenterId: healthCenterId,
      );

      await _localDataSource.saveMember(
        localMember,
        healthCenterId: healthCenterId,
      );
    }

    return response;
  }

  @override
  RepositoryResponse<MessageResponse> attachMember({
    required String healthCenterId,
    required String userId,
    required MemberType type,
    required MemberResponsibility responsibility,
  }) {
    return requestHelper(() => _remoteDataSource.attachMember(
          healthCenterId: healthCenterId,
          userId: userId,
          type: type,
          responsibility: responsibility,
        ));
  }

  @override
  RepositoryResponse<List<User>> searchAttachMember({
    required String healthCenterId,
    required MemberType type,
    required String search,
  }) async {
    final isOnline = await _connectionChecker.isOnline();

    if (isOnline) {
      return requestHelper(() => _remoteDataSource.searchAttachMember(
            healthCenterId: healthCenterId,
            type: type,
            search: search,
          ));
    } else {
      // Search in local members
      final localMembers = await _localDataSource.searchMembers(
        search,
        healthCenterId: healthCenterId,
      );
      final users = localMembers.map((m) => m.toEntity() as User).toList();

      if (users.isEmpty) {
        return Left(CacheFailure('Aucun membre trouvé hors ligne'));
      }

      return Right(users);
    }
  }
}
