import 'package:injectable/injectable.dart';
import 'package:s3g/core/objectbox/objectbox.dart';
import 'package:s3g/src/manager/member/data/local/models/member_local_model.dart';

abstract class MemberLocalDataSource {
  Future<void> saveMember(
    MemberLocalModel member, {
    required String healthCenterId,
  });
  Future<void> saveMembers(
    List<MemberLocalModel> members, {
    required String healthCenterId,
  });
  Future<MemberLocalModel?> getMemberById(
    String id, {
    required String healthCenterId,
  });
  Future<List<MemberLocalModel>> getAllMembers({
    required String healthCenterId,
  });
  Future<List<MemberLocalModel>> searchMembers(
    String query, {
    required String healthCenterId,
  });
  Future<void> clearAll();
}

@Injectable(as: MemberLocalDataSource)
class MemberLocalDataSourceImpl implements MemberLocalDataSource {
  final ObjectBox objectBox;

  MemberLocalDataSourceImpl({required this.objectBox});

  Box<MemberLocalModel> get _box => objectBox.store.box<MemberLocalModel>();

  @override
  Future<void> saveMember(
    MemberLocalModel member, {
    required String healthCenterId,
  }) async {
    // Check if member with this ID already exists
    final query = _box
        .query(
          MemberLocalModel_.id.equals(member.id) &
              MemberLocalModel_.healthCenterId.equals(healthCenterId),
        )
        .build();

    final existing = await query.findFirstAsync();
    query.close();

    if (existing != null) {
      // Update existing member
      existing.name = member.name;
      existing.email = member.email;
      existing.role = member.role;
      existing.responsibility = member.responsibility;
      existing.companionRole = member.companionRole;
      existing.healthCenterId = healthCenterId;
      existing.phone = member.phone;
      existing.description = member.description;
      existing.createdAt = member.createdAt;
      existing.updatedAt = member.updatedAt;
      await _box.putAsync(existing);
    } else {
      // Save new member
      await _box.putAsync(member);
    }
  }

  @override
  Future<void> saveMembers(
    List<MemberLocalModel> members, {
    required String healthCenterId,
  }) async {
    for (final member in members) {
      await saveMember(member, healthCenterId: healthCenterId);
    }
  }

  @override
  Future<MemberLocalModel?> getMemberById(
    String id, {
    required String healthCenterId,
  }) async {
    final query = _box
        .query(
          MemberLocalModel_.id.equals(id) &
              MemberLocalModel_.healthCenterId.equals(healthCenterId),
        )
        .build();
    final result = await query.findFirstAsync();
    query.close();
    return result;
  }

  @override
  Future<List<MemberLocalModel>> getAllMembers({
    required String healthCenterId,
  }) async {
    final query = _box
        .query(
          MemberLocalModel_.healthCenterId.equals(healthCenterId),
        )
        .build();

    final result = await query.findAsync();
    query.close();

    return result;
  }

  @override
  Future<List<MemberLocalModel>> searchMembers(
    String query, {
    required String healthCenterId,
  }) async {
    final lowercaseQuery = query.toLowerCase();

    // Get all members and filter in memory
    final allMembers = await getAllMembers(healthCenterId: healthCenterId);

    return allMembers.where((member) {
      return member.name.toLowerCase().contains(lowercaseQuery) ||
          (member.email?.toLowerCase().contains(lowercaseQuery) ?? false) ||
          member.phone.toLowerCase().contains(lowercaseQuery);
    }).toList();
  }

  @override
  Future<void> clearAll() async {
    await _box.removeAllAsync();
  }
}
