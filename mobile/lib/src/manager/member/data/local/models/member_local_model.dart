import 'package:objectbox/objectbox.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/manager/member/member.dart';

@Entity()
class MemberLocalModel {
  @Id()
  int oid = 0;

  @Index()
  String id;

  @Index()
  String healthCenterId;

  String name;
  String? email;
  String role;
  String responsibility;
  String? companionRole;
  String phone;
  String? description;

  @Property(type: PropertyType.date)
  DateTime createdAt;

  @Property(type: PropertyType.date)
  DateTime updatedAt;

  MemberLocalModel({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
    required this.healthCenterId,
    required this.responsibility,
    required this.companionRole,
    required this.phone,
    required this.description,
    required this.createdAt,
    required this.updatedAt,
  });

  factory MemberLocalModel.fromEntity(
    Member member, {
    required String healthCenterId,
  }) {
    return MemberLocalModel(
      id: member.id,
      name: member.name,
      email: member.email,
      role: member.role.name,
      healthCenterId: healthCenterId,
      responsibility: member.responsibility.name,
      companionRole: member.companionRole?.name,
      phone: member.phone,
      description: member.description,
      createdAt: member.createdAt,
      updatedAt: member.updatedAt,
    );
  }

  Member toEntity() {
    return Member(
      id: id,
      name: name,
      email: email,
      role: UserRole.values.firstWhere(
        (e) => e.name == role,
        orElse: () => UserRole.COMPANION,
      ),
      responsibility: MemberResponsibility.values.firstWhere(
        (e) => e.name == responsibility,
        orElse: () => MemberResponsibility.COMPANION,
      ),
      companionRole: companionRole != null
          ? MemberCompanionRole.values.firstWhere(
              (e) => e.name == companionRole,
              orElse: () => MemberCompanionRole.CODESA,
            )
          : null,
      phone: phone,
      description: description,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}
