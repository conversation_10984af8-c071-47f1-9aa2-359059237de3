import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:getwidget/getwidget.dart';
import 'package:go_router/go_router.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/manager/health_center/health_center.dart';
import 'package:s3g/src/manager/instance/instance.dart' show InstancesBloc;
import 'package:s3g/src/manager/manager.dart';
import 'package:s3g/src/manager/member/member.dart'
    show GetMembersCubit, MemberType;
import 'package:s3g/src/manager/questionnaire/questionnaire.dart'
    show GetQuestionnairesCubit;

import 'health_center_drawer.dart';

class HealthCenterScaffoldWrapper extends StatefulWidget {
  const HealthCenterScaffoldWrapper({Key? key, required this.navigationShell})
      : super(
          key: key ?? const ValueKey<String>('HealthCenterScaffoldWrapper'),
        );

  final StatefulNavigationShell navigationShell;

  @override
  State<HealthCenterScaffoldWrapper> createState() =>
      _HealthCenterScaffoldWrapperState();
}

class _HealthCenterScaffoldWrapperState
    extends State<HealthCenterScaffoldWrapper> {
  Future<void> _preloadData(String healthCenterId) async {
    // Preload some data, just to make sure they will be available when needed
    for (var type in MemberType.values) {
      GetMembersCubit(
        getMembers: getIt(),
        healthCenterId: healthCenterId,
        memberType: type,
      );
    }

    // Preload instances
    InstancesBloc(
      getIt(),
      healthCenterId: healthCenterId,
    ).preloadInstances();

    // Preload questionnaires
    getIt<GetQuestionnairesCubit>().getQuestionnaires();
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => getIt<HealthCenterCubit>()..loadHealthCenter(),
        ),
        BlocProvider(
          create: (context) => getIt<HealthCenterDrawerCubit>(),
        ),
      ],
      child: _HealthCenterScaffold(
        navigationShell: widget.navigationShell,
        onHealthCenterSelected: _preloadData,
      ),
    );
  }
}

class _HealthCenterScaffold extends StatelessWidget {
  const _HealthCenterScaffold({
    required this.navigationShell,
    required this.onHealthCenterSelected,
  });

  final StatefulNavigationShell navigationShell;
  final Function(String healthCenterId) onHealthCenterSelected;

  @override
  Widget build(BuildContext context) {
    final drawerState = context.watch<HealthCenterDrawerCubit>().state;

    return Scaffold(
      drawerEnableOpenDragGesture: false,
      drawer: BlocBuilder<HealthCenterCubit, HealthCenterState>(
        builder: (context, state) {
          if (state.healthCenters.length > 1) {
            return const HealthCenterDrawer();
          }
          return const SizedBox.shrink();
        },
      ),
      appBar: AppBar(
        leading: BlocBuilder<HealthCenterCubit, HealthCenterState>(
          builder: (context, state) {
            if (state.healthCenters.length > 1) {
              return IconButton(
                icon: const Icon(Icons.menu),
                onPressed: () {
                  if (drawerState.enabled) {
                    Scaffold.of(context).openDrawer();
                  }
                },
              );
            }
            return const AppBarLeadingTitle();
          },
        ),
        centerTitle: false,
        title: BlocBuilder<HealthCenterCubit, HealthCenterState>(
          builder: (context, state) {
            if (state.selectedHealthCenter != null) {
              return Text(
                state.selectedHealthCenter!.name,
                style: const TextStyle(fontSize: 15),
              );
            }

            return const SizedBox.shrink();
          },
        ),
        actions: const [
          HelpIcon(),
          UserNotifications(),
          Padding(
            padding: EdgeInsets.only(right: 15, left: 3),
            child: UserIcon(),
          ),
        ],
      ),
      body: BlocListener<HealthCenterCubit, HealthCenterState>(
        listener: (context, state) {
          // Call preload callback when health center is selected
          if (state.selectedHealthCenter != null) {
            onHealthCenterSelected(state.selectedHealthCenter!.id);
          }
        },
        child: BlocBuilder<HealthCenterCubit, HealthCenterState>(
          builder: (context, state) {
            switch (state.status) {
              case HealthCenterStatus.initial || HealthCenterStatus.loading:
                return const Center(
                  child: GFLoader(size: GFSize.LARGE),
                );

              case HealthCenterStatus.loaded:
                if (state.healthCenters.isEmpty) {
                  return const EmptyList(
                    message: 'Aucune donnée disponible.',
                  );
                }

                if (state.healthCenters.isNotEmpty &&
                    state.selectedHealthCenter == null) {
                  context
                      .read<HealthCenterCubit>()
                      .selectHealthCenter(state.healthCenters.first);
                }

                return SafeArea(child: navigationShell);

              case HealthCenterStatus.error:
                return RetryWidget(
                  message: state.errorMessage,
                  onPressed: () {
                    context.read<HealthCenterCubit>().loadHealthCenter();
                  },
                );
              // ignore: unreachable_switch_default
              default:
            }

            return const SizedBox.shrink();
          },
        ),
      ),
      bottomNavigationBar: _BottomNavigationBar(
        navigationShell: navigationShell,
      ),
    );
  }
}

class _BottomNavigationBar extends StatelessWidget {
  final StatefulNavigationShell navigationShell;

  const _BottomNavigationBar({required this.navigationShell});

  void _goBranch(int index) {
    navigationShell.goBranch(
      index,
      initialLocation: index == navigationShell.currentIndex,
    );
  }

  @override
  Widget build(BuildContext context) {
    return NavigationBar(
      labelBehavior: NavigationDestinationLabelBehavior.alwaysShow,
      selectedIndex: navigationShell.currentIndex,
      indicatorColor: Theme.of(context).colorScheme.primary,
      onDestinationSelected: (int index) => _goBranch(index),
      destinations: const <Widget>[
        NavigationDestination(
          selectedIcon: Icon(Icons.local_hospital, color: Colors.white),
          icon: Icon(Icons.local_hospital_rounded),
          label: 'Centre',
        ),
        NavigationDestination(
          selectedIcon: Icon(Icons.cases_outlined, color: Colors.white),
          icon: Icon(Icons.cases_outlined),
          label: 'Cas',
        ),
        NavigationDestination(
          selectedIcon: Icon(Icons.supervised_user_circle, color: Colors.white),
          icon: Icon(Icons.supervised_user_circle),
          label: 'Accomp...',
        ),
        NavigationDestination(
          selectedIcon: Icon(Icons.group, color: Colors.white),
          icon: Icon(Icons.group),
          label: 'APS',
        ),
      ],
    );
  }
}
