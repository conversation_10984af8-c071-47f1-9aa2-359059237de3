// ignore_for_file: overridden_fields

import 'package:json_annotation/json_annotation.dart';
import 'package:s3g/src/manager/health_center/domain/entity/health_center.dart';
import 'package:s3g/src/manager/member/member.dart';

import 'health_zone_model.dart';

part 'health_center_model.g.dart';

@JsonSerializable(explicitToJson: true)
class HealthCenterModel extends HealthCenter {
  @override
  @JsonKey(name: "created_at")
  final DateTime createdAt;

  @override
  @JsonKey(name: "updated_at")
  final DateTime updatedAt;

  @override
  @JsonKey(name: "health_zone")
  final HealthZoneModel healthZone;

  @override
  @JsonKey(name: "services_offered")
  final String? servicesOffered;

  const HealthCenterModel({
    required super.id,
    required super.name,
    required super.address,
    required super.phone,
    required super.aps,
    required super.companions,
    required super.instances,
    required super.responsibility,
    required super.relapses,
    required this.servicesOffered,
    required this.healthZone,
    required this.createdAt,
    required this.updatedAt,
  }) : super(
          createdAt: createdAt,
          updatedAt: updatedAt,
          healthZone: healthZone,
          servicesOffered: servicesOffered,
        );

  factory HealthCenterModel.fromJson(Map<String, dynamic> json) =>
      _$HealthCenterModelFromJson(json);

  Map<String, dynamic> toJson() => _$HealthCenterModelToJson(this);

  // Convert from domain entity to model
  factory HealthCenterModel.fromEntity(HealthCenter entity) {
    return HealthCenterModel(
      id: entity.id,
      name: entity.name,
      address: entity.address,
      phone: entity.phone,
      aps: entity.aps,
      companions: entity.companions,
      instances: entity.instances,
      responsibility: entity.responsibility,
      relapses: entity.relapses,
      servicesOffered: entity.servicesOffered,
      healthZone: entity.healthZone is HealthZoneModel
          ? entity.healthZone as HealthZoneModel
          : HealthZoneModel.fromEntity(entity.healthZone),
      createdAt: entity.createdAt,
      updatedAt: entity.updatedAt,
    );
  }
}
