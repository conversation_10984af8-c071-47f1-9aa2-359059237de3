import 'dart:convert';

import 'package:objectbox/objectbox.dart';
import 'package:s3g/src/manager/health_center/data/remote/models/health_zone_model.dart';
import 'package:s3g/src/manager/health_center/domain/entity/health_center.dart';
import 'package:s3g/src/manager/health_center/domain/entity/health_zone.dart';
import 'package:s3g/src/manager/member/member.dart';

@Entity()
class HealthCenterLocalModel {
  @Id()
  int oid = 0;

  @Index()
  @Unique()
  String id;

  String name;
  String address;
  String phone;
  String? servicesOffered;
  int aps;
  int companions;
  int instances;
  int relapses;

  // Store responsibility as string (can be null)
  String? responsibility;

  // Store health zone as JSON
  String healthZoneJson;

  @Property(type: PropertyType.date)
  DateTime createdAt;

  @Property(type: PropertyType.date)
  DateTime updatedAt;

  // Sync metadata
  @Property(type: PropertyType.date)
  DateTime? lastSyncedAt;

  HealthCenterLocalModel({
    this.oid = 0,
    required this.id,
    required this.name,
    required this.address,
    required this.phone,
    this.servicesOffered,
    required this.aps,
    required this.companions,
    required this.instances,
    required this.relapses,
    this.responsibility,
    required this.healthZoneJson,
    required this.createdAt,
    required this.updatedAt,
    this.lastSyncedAt,
  });

  // Convert from domain entity
  factory HealthCenterLocalModel.fromEntity(HealthCenter healthCenter) {
    return HealthCenterLocalModel(
      id: healthCenter.id,
      name: healthCenter.name,
      address: healthCenter.address,
      phone: healthCenter.phone,
      servicesOffered: healthCenter.servicesOffered,
      aps: healthCenter.aps,
      companions: healthCenter.companions,
      instances: healthCenter.instances,
      relapses: healthCenter.relapses,
      responsibility: healthCenter.responsibility?.name,
      healthZoneJson: _healthZoneToJson(healthCenter.healthZone),
      createdAt: healthCenter.createdAt,
      updatedAt: healthCenter.updatedAt,
      lastSyncedAt: DateTime.now(),
    );
  }

  // Convert to domain entity
  HealthCenter toEntity() {
    return HealthCenter(
      id: id,
      name: name,
      address: address,
      phone: phone,
      servicesOffered: servicesOffered,
      aps: aps,
      companions: companions,
      instances: instances,
      relapses: relapses,
      responsibility: responsibility != null
          ? MemberResponsibility.values
              .firstWhere((e) => e.name == responsibility)
          : null,
      healthZone: _healthZoneFromJson(healthZoneJson),
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  // Helper method to convert HealthZone to JSON
  static String _healthZoneToJson(HealthZone healthZone) {
    final model = healthZone is HealthZoneModel
        ? healthZone
        : HealthZoneModel.fromEntity(healthZone);
    return jsonEncode(model.toJson());
  }

  // Helper method to convert JSON to HealthZone
  static HealthZone _healthZoneFromJson(String json) {
    final data = jsonDecode(json) as Map<String, dynamic>;
    return HealthZoneModel.fromJson(data);
  }

  // Update from server
  void updateFromServer(HealthCenter healthCenter) {
    name = healthCenter.name;
    address = healthCenter.address;
    phone = healthCenter.phone;
    servicesOffered = healthCenter.servicesOffered;
    aps = healthCenter.aps;
    companions = healthCenter.companions;
    instances = healthCenter.instances;
    relapses = healthCenter.relapses;
    responsibility = healthCenter.responsibility?.name;
    healthZoneJson = _healthZoneToJson(healthCenter.healthZone);
    updatedAt = healthCenter.updatedAt;
    lastSyncedAt = DateTime.now();
  }
}
