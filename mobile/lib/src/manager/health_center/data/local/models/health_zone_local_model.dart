import 'package:objectbox/objectbox.dart';
import 'package:s3g/src/manager/health_center/data/remote/models/health_zone_model.dart';
import 'package:s3g/src/manager/health_center/domain/entity/health_zone.dart';

@Entity()
class HealthZoneLocalModel {
  @Id()
  int oid = 0;

  @Index()
  @Unique()
  String id;

  String name;
  int populationServed;

  @Property(type: PropertyType.date)
  DateTime createdAt;

  @Property(type: PropertyType.date)
  DateTime updatedAt;

  HealthZoneLocalModel({
    this.oid = 0,
    required this.id,
    required this.name,
    required this.populationServed,
    required this.createdAt,
    required this.updatedAt,
  });

  // Convert from domain entity
  factory HealthZoneLocalModel.fromEntity(HealthZone healthZone) {
    return HealthZoneLocalModel(
      id: healthZone.id,
      name: healthZone.name,
      populationServed: healthZone.populationServed,
      createdAt: healthZone.createdAt,
      updatedAt: healthZone.updatedAt,
    );
  }

  // Convert to domain entity
  HealthZone toEntity() {
    return HealthZone(
      id: id,
      name: name,
      populationServed: populationServed,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  // Convert from JSON (from HealthZoneModel)
  factory HealthZoneLocalModel.fromJson(Map<String, dynamic> json) {
    final model = HealthZoneModel.fromJson(json);
    return HealthZoneLocalModel.fromEntity(model);
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return HealthZoneModel.fromEntity(toEntity()).toJson();
  }
}
