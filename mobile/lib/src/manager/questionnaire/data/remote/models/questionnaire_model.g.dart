// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'questionnaire_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

QuestionnaireModel _$QuestionnaireModelFromJson(Map<String, dynamic> json) =>
    QuestionnaireModel(
      id: json['id'] as String,
      question: json['question'] as String,
      type: $enumDecode(_$QuestionnaireResponseTypeEnumMap, json['type']),
      hint: json['hint'] as String?,
      choices: (json['choices'] as List<dynamic>?)
          ?.map((e) =>
              QuestionnaireChoiceModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$QuestionnaireModelToJson(QuestionnaireModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'question': instance.question,
      'type': _$QuestionnaireResponseTypeEnumMap[instance.type]!,
      'hint': instance.hint,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'choices': instance.choices,
    };

const _$QuestionnaireResponseTypeEnumMap = {
  QuestionnaireResponseType.TEXT: 'TEXT',
  QuestionnaireResponseType.CHOICES: 'CHOICES',
  QuestionnaireResponseType.BOOLEAN: 'BOOLEAN',
  QuestionnaireResponseType.NUMBER: 'NUMBER',
  QuestionnaireResponseType.DATE: 'DATE',
  QuestionnaireResponseType.ATTACHMENT: 'ATTACHMENT',
};

QuestionnaireChoiceModel _$QuestionnaireChoiceModelFromJson(
        Map<String, dynamic> json) =>
    QuestionnaireChoiceModel(
      id: json['id'] as String,
      choice: json['choice'] as String,
      questionnaireId: json['questionnaire_id'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$QuestionnaireChoiceModelToJson(
        QuestionnaireChoiceModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'choice': instance.choice,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'questionnaire_id': instance.questionnaireId,
    };
