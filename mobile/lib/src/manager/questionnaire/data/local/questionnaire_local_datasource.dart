import 'package:injectable/injectable.dart';
import 'package:s3g/core/objectbox/objectbox.dart';
import 'package:s3g/src/manager/questionnaire/data/local/models/questionnaire_local_model.dart';

abstract class QuestionnaireLocalDataSource {
  Future<void> saveQuestionnaire(QuestionnaireLocalModel questionnaire);
  Future<void> saveQuestionnaires(List<QuestionnaireLocalModel> questionnaires);
  Future<QuestionnaireLocalModel?> getQuestionnaireById(String id);
  Future<List<QuestionnaireLocalModel>> getAllQuestionnaires();
  Future<void> clearAllQuestionnaires();
}

@Injectable(as: QuestionnaireLocalDataSource)
class QuestionnaireLocalDataSourceImpl implements QuestionnaireLocalDataSource {
  final ObjectBox objectBox;

  QuestionnaireLocalDataSourceImpl({required this.objectBox});

  Box<QuestionnaireLocalModel> get _questionnaireBox =>
      objectBox.store.box<QuestionnaireLocalModel>();

  @override
  Future<void> saveQuestionnaire(QuestionnaireLocalModel questionnaire) async {
    // Check if questionnaire with this ID already exists
    final query = _questionnaireBox
        .query(QuestionnaireLocalModel_.id.equals(questionnaire.id))
        .build();
    final existing = await query.findFirstAsync();
    query.close();

    if (existing != null) {
      // Update existing questionnaire
      existing.question = questionnaire.question;
      existing.type = questionnaire.type;
      existing.hint = questionnaire.hint;
      existing.choicesJson = questionnaire.choicesJson;
      existing.createdAt = questionnaire.createdAt;
      existing.updatedAt = questionnaire.updatedAt;
      await _questionnaireBox.putAsync(existing);
    } else {
      // Save new questionnaire
      await _questionnaireBox.putAsync(questionnaire);
    }
  }

  @override
  Future<void> saveQuestionnaires(
      List<QuestionnaireLocalModel> questionnaires) async {
    for (final questionnaire in questionnaires) {
      await saveQuestionnaire(questionnaire);
    }
  }

  @override
  Future<QuestionnaireLocalModel?> getQuestionnaireById(String id) async {
    final query =
        _questionnaireBox.query(QuestionnaireLocalModel_.id.equals(id)).build();
    final result = await query.findFirstAsync();
    query.close();
    return result;
  }

  @override
  Future<List<QuestionnaireLocalModel>> getAllQuestionnaires() async {
    return await _questionnaireBox.getAllAsync();
  }

  @override
  Future<void> clearAllQuestionnaires() async {
    await _questionnaireBox.removeAllAsync();
  }
}
