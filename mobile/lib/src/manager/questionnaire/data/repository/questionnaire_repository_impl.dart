import 'package:injectable/injectable.dart';
import 'package:s3g/core/errors/failures.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/network/connection_checker.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/src/manager/questionnaire/data/local/models/questionnaire_local_model.dart';
import 'package:s3g/src/manager/questionnaire/data/local/questionnaire_local_datasource.dart';
import 'package:s3g/src/manager/questionnaire/domain/entity/questionnaire.dart';
import 'package:fpdart/fpdart.dart';

import '../../domain/repository/questionnaire_repository.dart';
import '../remote/questionnaire_remote_datasource.dart';

@Injectable(as: QuestionnaireRepository)
class QuestionnaireRepositoryImpl extends QuestionnaireRepository {
  final QuestionnaireRemoteDataSource _remoteDataSource;
  final QuestionnaireLocalDataSource _localDataSource;
  final ConnectionChecker _connectionChecker;

  QuestionnaireRepositoryImpl(
    this._remoteDataSource,
    this._localDataSource,
    this._connectionChecker,
  );

  @override
  RepositoryResponse<List<Questionnaire>> getQuestionnaires() async {
    final isOnline = await _connectionChecker.isOnline();

    if (isOnline) {
      final response =
          await requestHelper(() => _remoteDataSource.getQuestionnaires());

      if (response.isRight()) {
        final questionnaires = response.fold(
          (l) => <Questionnaire>[],
          (r) => r,
        );

        // Cache questionnaires locally
        final localQuestionnaires = questionnaires
            .map((questionnaire) =>
                QuestionnaireLocalModel.fromEntity(questionnaire))
            .toList();

        await _localDataSource.saveQuestionnaires(localQuestionnaires);
      }

      return response;
    } else {
      // Get from local storage
      final localQuestionnaires = await _localDataSource.getAllQuestionnaires();
      final questionnaires =
          localQuestionnaires.map((q) => q.toEntity()).toList();

      if (questionnaires.isEmpty) {
        return Left(CacheFailure('Aucun questionnaire disponible hors ligne'));
      }

      return Right(questionnaires);
    }
  }
}
