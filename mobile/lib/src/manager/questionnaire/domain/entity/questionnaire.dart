// ignore_for_file: constant_identifier_names

import 'package:equatable/equatable.dart';

class Questionnaire extends Equatable {
  const Questionnaire({
    required this.id,
    required this.question,
    required this.type,
    required this.hint,
    required this.createdAt,
    required this.updatedAt,
    required this.choices,
  });

  final String id;

  final String question;

  final QuestionnaireResponseType type;

  final List<QuestionnaireChoice>? choices;

  final String? hint;

  final DateTime createdAt;

  final DateTime updatedAt;

  @override
  List<Object?> get props => [id];
}

class QuestionnaireChoice extends Equatable {
  const QuestionnaireChoice({
    required this.id,
    required this.choice,
    required this.questionnaireId,
    required this.createdAt,
    required this.updatedAt,
  });

  final String id;

  final String questionnaireId;

  final String choice;

  final DateTime createdAt;

  final DateTime updatedAt;

  @override
  List<Object?> get props => [id];
}

enum QuestionnaireResponseType {
  TEXT,
  CHOICES,
  BOOLEAN,
  NUMBER,
  DATE,
  ATTACHMENT;

  getLabel() {
    return switch (this) {
      TEXT => "Texte",
      CHOICES => "Choix Multiple",
      BOOLEAN => "Oui_Non",
      NUMBER => "Nombre",
      DATE => "Date",
      ATTACHMENT => "Pièce jointe",
    };
  }
}
