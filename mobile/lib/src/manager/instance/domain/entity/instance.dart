// ignore_for_file: constant_identifier_names

import 'package:equatable/equatable.dart';
import 'package:s3g/src/authentication/authentication.dart';

import 'package:s3g/src/manager/health_center/health_center.dart';
import 'package:s3g/src/manager/instance_features/relapse/relapse.dart';

part 'survivor.dart';

enum InstanceType {
  NEW,
  RELAPSE;

  String getLabel() {
    return switch (this) {
      NEW => "Nouveau",
      RELAPSE => "Rechute",
    };
  }
}

enum InstanceStatus {
  OPEN,
  CLOSED;

  String getLabel() {
    return switch (this) {
      OPEN => 'Ouvert',
      CLOSED => 'Dechargé',
    };
  }
}

class Instance extends Equatable {
  final String id;
  final String code;
  final InstanceType type;
  final InstanceStatus status;
  final String survivorCode;
  final DateTime? relapsedAt;
  final String healthCenterId;
  final String? description;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Instance({
    required this.id,
    required this.code,
    required this.type,
    required this.survivorCode,
    required this.relapsedAt,
    required this.healthCenterId,
    required this.description,
    required this.createdAt,
    required this.updatedAt,
    required this.status,
  });

  @override
  List<Object?> get props => [id];
}

class InstanceShow extends Instance {
  final HealthCenter? healthCenter;
  final Survivor? survivor;
  final Relapse? relapse;
  final User? creator;

  const InstanceShow({
    required super.id,
    required super.code,
    required super.type,
    required super.status,
    required super.description,
    required super.createdAt,
    required super.updatedAt,
    required super.survivorCode,
    required super.relapsedAt,
    required super.healthCenterId,
    required this.relapse,
    required this.survivor,
    required this.healthCenter,
    required this.creator,
  });

  @override
  List<Object?> get props => throw UnimplementedError();
}
