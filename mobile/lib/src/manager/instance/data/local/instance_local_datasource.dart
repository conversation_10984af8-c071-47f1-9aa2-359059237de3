import 'dart:developer';

import 'package:injectable/injectable.dart';
import 'package:s3g/core/objectbox/objectbox.dart';
import 'package:s3g/src/manager/instance/data/local/models/instance_local_model.dart';
import 'package:s3g/src/sync/sync_service.dart' show SyncStatus;

abstract class InstanceLocalDataSource {
  Future<List<InstanceLocalModel>> getAllInstances();
  Future<List<InstanceLocalModel>> getInstancesByHealthCenter(
    String healthCenterId,
  );
  Future<List<InstanceLocalModel>> getPendingSyncInstances();
  Future<InstanceLocalModel?> getInstanceById(String id);
  Future<InstanceLocalModel?> getInstanceByLocalId(String localId);
  Future<void> saveInstance(InstanceLocalModel instance);
  Future<void> saveInstances(List<InstanceLocalModel> instances);
  Future<void> clearAllInstances();
  Future<List<InstanceLocalModel>> searchInstances(
    String query, {
    String? healthCenterId,
  });

  Future<void> deleteInstance(String localId);
  Future<void> deleteInstanceById(String id);

  Future<void> markInstanceAsDeleted(String localId);
  Future<void> markInstanceAsDeletedById(String id);

  Future<void> markInstanceAsSynced(String localId, String serverId);
  Future<void> markInstanceSyncFailed(String localId, String error);

  Future<void> updateInstanceFromServer(InstanceLocalModel serverInstance);
  Future<void> updateInstanceContent(
    String localId, {
    String? status,
    String? description,
  });
}

@Injectable(as: InstanceLocalDataSource)
class InstanceLocalDataSourceImpl implements InstanceLocalDataSource {
  final ObjectBox objectBox;

  InstanceLocalDataSourceImpl({required this.objectBox});

  Box<InstanceLocalModel> get _box => objectBox.store.box<InstanceLocalModel>();

  @override
  Future<List<InstanceLocalModel>> getAllInstances() async {
    final query =
        _box.query(InstanceLocalModel_.isDeleted.equals(false)).build();
    final instances = await query.findAsync();
    query.close();
    return instances;
  }

  @override
  Future<List<InstanceLocalModel>> getInstancesByHealthCenter(
      String healthCenterId) async {
    final query = _box
        .query(InstanceLocalModel_.healthCenterId.equals(healthCenterId) &
            InstanceLocalModel_.isDeleted.equals(false))
        .build();
    final instances = await query.findAsync();
    query.close();
    return instances;
  }

  @override
  Future<List<InstanceLocalModel>> getPendingSyncInstances() async {
    final query = _box
        .query(
          (InstanceLocalModel_.syncStatus.equals(SyncStatus.pending.value) |
              InstanceLocalModel_.syncStatus.equals(SyncStatus.failed.value)),
        )
        .build();
    final instances = await query.findAsync();
    query.close();
    return instances;
  }

  @override
  Future<InstanceLocalModel?> getInstanceById(String id) async {
    final query = _box
        .query(InstanceLocalModel_.id.equals(id) &
            InstanceLocalModel_.isDeleted.equals(false))
        .build();
    final instance = await query.findFirstAsync();
    query.close();
    return instance;
  }

  @override
  Future<InstanceLocalModel?> getInstanceByLocalId(String localId) async {
    final query = _box
        .query(InstanceLocalModel_.localId.equals(localId) &
            InstanceLocalModel_.isDeleted.equals(false))
        .build();
    final instance = await query.findFirstAsync();
    query.close();
    return instance;
  }

  @override
  Future<void> saveInstance(InstanceLocalModel instance) async {
    // Check if an instance with this server ID already exists
    if (instance.id != null) {
      final query =
          _box.query(InstanceLocalModel_.id.equals(instance.id!)).build();
      final existing = await query.findFirstAsync();
      query.close();

      if (existing != null) {
        // Update the existing record
        existing.code = instance.code;
        existing.type = instance.type;
        existing.status = instance.status;
        existing.survivorCode = instance.survivorCode;
        existing.healthCenterId = instance.healthCenterId;
        existing.description = instance.description;
        existing.createdAt = instance.createdAt;
        existing.updatedAt = instance.updatedAt;
        existing.isDeleted = instance.isDeleted;
        existing.syncStatus = instance.syncStatus;
        existing.syncError = instance.syncError;
        existing.lastSyncAttempt = instance.lastSyncAttempt;
        existing.relapsedAt = instance.relapsedAt;
        existing.survivorJson = instance.survivorJson;
        existing.healthCenterJson = instance.healthCenterJson;
        existing.relapseJson = instance.relapseJson;
        existing.creatorJson = instance.creatorJson;
        await _box.putAsync(existing);
        return;
      }
    }

    // No existing record found, save as new
    await _box.putAsync(instance);
  }

  @override
  Future<void> saveInstances(List<InstanceLocalModel> instances) async {
    // Process each instance individually to handle updates
    for (final instance in instances) {
      await saveInstance(instance);
    }
  }

  @override
  Future<void> markInstanceAsDeleted(String localId) async {
    final instance = await getInstanceByLocalId(localId);
    if (instance != null) {
      instance.isDeleted = true;
      instance.updatedAt = DateTime.now();

      // If it's not synced yet, mark for deletion sync
      if (instance.needsSync) {
        instance.syncStatus = SyncStatus.pending.value;
      }

      await saveInstance(instance);
    }
  }

  @override
  Future<void> markInstanceAsDeletedById(String id) async {
    final instance = await getInstanceById(id);
    if (instance != null) {
      instance.isDeleted = true;
      instance.updatedAt = DateTime.now();
      instance.syncStatus = SyncStatus.pending.value;
      await saveInstance(instance);
    }
  }

  @override
  Future<void> clearAllInstances() async {
    await _box.removeAllAsync();
  }

  @override
  Future<List<InstanceLocalModel>> searchInstances(String query,
      {String? healthCenterId}) async {
    Condition<InstanceLocalModel> condition =
        InstanceLocalModel_.isDeleted.equals(false);

    if (healthCenterId != null) {
      condition =
          condition & InstanceLocalModel_.healthCenterId.equals(healthCenterId);
    }

    // Search in code, survivor code, and description
    condition = condition &
        (InstanceLocalModel_.code.contains(query, caseSensitive: false) |
            InstanceLocalModel_.survivorCode
                .contains(query, caseSensitive: false) |
            InstanceLocalModel_.description
                .contains(query, caseSensitive: false));

    final builtQuery = _box.query(condition).build();
    final instances = await builtQuery.findAsync();
    builtQuery.close();
    return instances;
  }

  // Helper methods for sync operations
  @override
  Future<void> markInstanceAsSynced(String localId, String serverId) async {
    final instance = await getInstanceByLocalId(localId);
    if (instance != null) {
      instance.markAsSynced(serverId);
      await saveInstance(instance);
    }
  }

  @override
  Future<void> markInstanceSyncFailed(String localId, String error) async {
    final instance = await getInstanceByLocalId(localId);
    if (instance != null) {
      instance.updateSyncStatus(SyncStatus.failed, error: error);
      await saveInstance(instance);
    }
  }

  @override
  Future<void> updateInstanceFromServer(
      InstanceLocalModel serverInstance) async {
    // Check if we have a local version by server ID
    final existingById = serverInstance.id != null
        ? await getInstanceById(serverInstance.id!)
        : null;

    if (existingById != null) {
      // Check if local instance has pending changes
      if (existingById.needsSync) {
        // Local changes take priority - don't overwrite
        log('Instance ${existingById.id} has pending local changes, skipping server update');
        return;
      }

      // Only update if server version is newer
      if (serverInstance.updatedAt.isAfter(existingById.updatedAt)) {
        // Update existing instance with server data, preserving the ObjectBox ID
        existingById.code = serverInstance.code;
        existingById.type = serverInstance.type;
        existingById.status = serverInstance.status;
        existingById.survivorCode = serverInstance.survivorCode;
        existingById.description = serverInstance.description;
        existingById.updatedAt = serverInstance.updatedAt;
        existingById.syncStatus = SyncStatus.synced.value;
        existingById.syncError = null;
        await saveInstance(existingById);
      }
    } else {
      // Check if we have any instance with the same ID (including deleted ones)
      final query =
          _box.query(InstanceLocalModel_.id.equals(serverInstance.id!)).build();
      final conflictingInstance = await query.findFirstAsync();
      query.close();

      if (conflictingInstance != null) {
        // Check if local instance has pending changes
        if (conflictingInstance.needsSync) {
          // Local changes take priority - don't overwrite
          log('Instance ${conflictingInstance.id} has pending local changes, skipping server update');
          return;
        }

        // Only update if server version is newer
        if (serverInstance.updatedAt.isAfter(conflictingInstance.updatedAt)) {
          // Update the existing instance (even if deleted) instead of creating a new one
          conflictingInstance.code = serverInstance.code;
          conflictingInstance.type = serverInstance.type;
          conflictingInstance.status = serverInstance.status;
          conflictingInstance.survivorCode = serverInstance.survivorCode;
          conflictingInstance.healthCenterId = serverInstance.healthCenterId;
          conflictingInstance.description = serverInstance.description;
          conflictingInstance.createdAt = serverInstance.createdAt;
          conflictingInstance.updatedAt = serverInstance.updatedAt;
          conflictingInstance.syncStatus = SyncStatus.synced.value;
          conflictingInstance.syncError = null;
          conflictingInstance.isDeleted = false; // Resurrect if it was deleted
          await saveInstance(conflictingInstance);
        }
      } else {
        // Save new instance from server
        serverInstance.syncStatus = SyncStatus.synced.value;
        await saveInstance(serverInstance);
      }
    }
  }

  @override
  Future<void> updateInstanceContent(String localId,
      {String? status, String? description}) async {
    final instance = await getInstanceByLocalId(localId);
    if (instance != null) {
      // Update fields if provided
      if (status != null) {
        instance.status = status;
      }
      if (description != null) {
        instance.description = description;
      }

      // Update metadata
      instance.updatedAt = DateTime.now();
      instance.syncStatus = SyncStatus.pending.value;

      await saveInstance(instance);
    }
  }

  @override
  Future<void> deleteInstance(String localId) async {
    final query =
        _box.query(InstanceLocalModel_.localId.equals(localId)).build();
    final instance = await query.findFirstAsync();
    query.close();

    if (instance != null) {
      await _box.removeAsync(instance.oid);
    }
  }

  @override
  Future<void> deleteInstanceById(String id) async {
    final query = _box.query(InstanceLocalModel_.id.equals(id)).build();
    final instance = await query.findFirstAsync();
    query.close();

    if (instance != null) {
      await _box.removeAsync(instance.oid);
    }
  }
}
