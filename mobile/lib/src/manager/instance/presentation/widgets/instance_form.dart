import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:go_router/go_router.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/common/widgets/sync_status_widget.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/routes/app_route.dart';
import 'package:s3g/src/manager/health_center/health_center.dart';
import 'package:s3g/src/manager/instance/domain/usecase/create_instance.dart';
import 'package:s3g/src/manager/instance/domain/usecase/edit_instance.dart';
import 'package:s3g/src/manager/instance/instance.dart';

import '../blocs/blocs.dart';

class InstanceForm extends StatelessWidget {
  final VoidCallback closeEditMode;
  final HealthCenter healthCenter;
  final Instance? editable;

  const InstanceForm({
    super.key,
    required this.closeEditMode,
    required this.healthCenter,
    required this.editable,
  });

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (_) => getIt<CreateInstanceCubit>(),
        ),
        BlocProvider(
          create: (_) => getIt<EditInstanceCubit>(),
        ),
      ],
      child: _InstanceFormContent(
        closeEditMode: closeEditMode,
        healthCenter: healthCenter,
        editable: editable,
      ),
    );
  }
}

class _InstanceFormContent extends StatefulWidget {
  final VoidCallback closeEditMode;
  final HealthCenter healthCenter;
  final Instance? editable;

  const _InstanceFormContent({
    required this.closeEditMode,
    required this.healthCenter,
    required this.editable,
  });

  @override
  State<_InstanceFormContent> createState() => _InstanceFormContentState();
}

class _InstanceFormContentState extends State<_InstanceFormContent> {
  final _formKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  widget.editable != null
                      ? "Modifier le cas ${widget.editable!.code}:"
                      : "Créer un nouveau cas:",
                  style: const TextStyle(fontSize: 18),
                ),
              ),
              const SyncStatusWidget(),
            ],
          ),
          columnSizedBox,

          // title field
          FormBuilderTextField(
            name: 'survivor_code',
            keyboardType: TextInputType.text,
            initialValue: widget.editable?.survivorCode,
            readOnly: widget.editable != null,
            autocorrect: false,
            enableSuggestions: false,
            textCapitalization: TextCapitalization.characters,
            validator: FormBuilderValidators.compose([
              FormBuilderValidators.required(),
              FormBuilderValidators.minLength(2),
              FormBuilderValidators.maxLength(150),
            ]),
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              labelText: 'Code Survivant',
              hintText: "Entrez le code survivant",
            ),
          ),

          // Some space
          columnSizedBox,

          // Type
          FormBuilderChoiceChips<InstanceType>(
            name: "type",
            enabled: widget.editable == null,
            spacing: 15,
            selectedColor:
                Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
            initialValue: widget.editable?.type ?? InstanceType.NEW,
            validator: FormBuilderValidators.required(),
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              labelText: 'Type cas',
              hintText: "Entrez le type cas",
            ),
            options:
                InstanceType.values.map<FormBuilderChipOption<InstanceType>>(
              (InstanceType v) {
                return FormBuilderChipOption(
                  value: v,
                  child: Text(v.getLabel()),
                );
              },
            ).toList(),
          ),

          columnSizedBox,

          // Description
          FormBuilderTextField(
            name: 'description',
            minLines: 3,
            maxLines: 10,
            initialValue: widget.editable?.description,
            keyboardType: TextInputType.multiline,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              labelText: 'Description',
              hintText: "Entrez la description",
            ),
          ),

          columnSizedBox,

          // Create or update buttons
          if (widget.editable != null)
            BlocBuilder<EditInstanceCubit, EditInstanceState>(
              builder: (_, state) {
                return FormButtons(
                  loading: state is EditInstanceLoading,
                  onSubmit: _save,
                  onCancel: widget.closeEditMode,
                );
              },
            ),

          if (widget.editable == null)
            BlocBuilder<CreateInstanceCubit, CreateInstanceState>(
              builder: (_, state) {
                return FormButtons(
                  loading: state is CreateInstanceLoading,
                  onSubmit: _save,
                  onCancel: widget.closeEditMode,
                );
              },
            ),

          // Show error message on create
          BlocListener<CreateInstanceCubit, CreateInstanceState>(
            listener: (_, state) {
              switch (state) {
                case CreateInstanceSuccess(
                    instance: InstanceShow instance,
                    isOffline: final isOffline
                  ):
                  widget.closeEditMode();
                  _formKey.currentState?.reset();

                  // Show success message with offline indicator
                  if (isOffline) {
                    showToast(
                      context,
                      message:
                          'Cas créé hors ligne. Il sera synchronisé automatiquement.',
                      type: ToastType.info,
                    );
                  } else {
                    showToast(
                      context,
                      message: 'Cas créé avec succès',
                      type: ToastType.success,
                    );
                  }

                  context.pushNamed(
                    AppRoute.managerInstance,
                    pathParameters: {'id': instance.id},
                  );

                  // Refresh the list
                  context
                      .read<InstancesBloc>()
                      .add(PaginatedItemFetched(refresh: true));

                  context
                      .read<InstancesRelapsedBloc>()
                      .add(PaginatedItemFetched(refresh: true));
                  break;

                case CreateInstanceError():
                  showToast(
                    context,
                    message: state.error,
                    type: ToastType.error,
                  );
                  break;

                default:
              }
            },
            child: const SizedBox.shrink(),
          ),

          // Show error message on edit
          BlocListener<EditInstanceCubit, EditInstanceState>(
            listener: (_, state) {
              switch (state) {
                case EditInstanceSuccess():
                  widget.closeEditMode();
                  _formKey.currentState?.reset();

                  // Refresh the list
                  context
                      .read<InstancesBloc>()
                      .add(PaginatedItemFetched(refresh: true));

                  context
                      .read<InstancesRelapsedBloc>()
                      .add(PaginatedItemFetched(refresh: true));
                  break;

                case EditInstanceError():
                  showToast(
                    context,
                    message: state.error,
                    type: ToastType.error,
                  );
                  break;

                default:
              }
            },
            child: const SizedBox.shrink(),
          )
        ],
      ),
    );
  }

  void _save() {
    final isValid = _formKey.currentState?.saveAndValidate();
    final value = _formKey.currentState?.value;

    if (isValid != null && isValid && value != null) {
      final survivorCode = value['survivor_code'] as String;
      final type = value['type'] as InstanceType;
      final description = value['description'] as String?;

      if (widget.editable != null) {
        context.read<EditInstanceCubit>().editInstance(
              EditInstanceParams(
                healthCenterId: widget.healthCenter.id,
                instanceId: widget.editable!.id,
                status: widget.editable!.status,
                description: description,
              ),
            );
      } else {
        context.read<CreateInstanceCubit>().createInstance(
              CreateInstanceParams(
                healthCenterId: widget.healthCenter.id,
                survivorCode: survivorCode,
                type: type,
                description: description,
              ),
            );
      }
    }
  }
}
