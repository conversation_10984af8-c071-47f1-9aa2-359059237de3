import 'package:bloc/bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:meta/meta.dart';
import 'package:s3g/core/network/connection_checker.dart';
import 'package:s3g/src/manager/instance/domain/usecase/create_instance.dart';

import '../../../domain/entity/instance.dart';

part 'create_instance_state.dart';

@injectable
class CreateInstanceCubit extends Cubit<CreateInstanceState> {
  final CreateInstance _createInstance;
  final ConnectionChecker _connectionChecker;

  CreateInstanceCubit(
    this._createInstance,
    this._connectionChecker,
  ) : super(CreateInstanceInitial());

  Future<void> createInstance(CreateInstanceParams params) async {
    emit(CreateInstanceLoading());

    // Check if device is online
    final isOnline = await _connectionChecker.isOnline();

    final instance = await _createInstance(params);

    instance.fold(
      (failure) => emit(CreateInstanceError(failure.message)),
      (instance) => emit(CreateInstanceSuccess(
        instance,
        isOffline: !isOnline,
      )),
    );
  }
}
