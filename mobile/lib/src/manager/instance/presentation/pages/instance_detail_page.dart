import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/config/theme.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/manager/instance/instance.dart';
import 'package:s3g/src/manager/instance_features/relapse/relapse.dart';

import '../../domain/usecase/edit_instance.dart';
import '../blocs/blocs.dart';
import '../widgets/instance_relapse.dart';

class InstanceDetailPage extends StatelessWidget {
  const InstanceDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    final instance =
        (context.read<InstanceDetailCubit>().state as InstanceDetailLoaded)
            .instance;

    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => getIt<DeleteInstanceCubit>(),
        ),
        BlocProvider(
          create: (context) => getIt<EditInstanceCubit>(),
        ),
        BlocProvider(
          create: (context) => InlineFormCubit<InstanceShow>(),
        ),
        BlocProvider(
          key: ValueKey(instance.id),
          create: (context) => RelapseCubit(
            instance.id,
            createRelapseUseCase: getIt(),
            updateRelapseUseCase: getIt(),
            deleteRelapseUseCase: getIt(),
            getRelapseUseCase: getIt(),
          ),
        )
      ],
      child: _InstanceDetailContent(instance),
    );
  }
}

class _InstanceDetailContent extends StatelessWidget {
  final InstanceShow instance;

  const _InstanceDetailContent(this.instance);

  @override
  Widget build(BuildContext context) {
    final editMode = context.watch<InlineFormCubit<InstanceShow>>();

    if (editMode.state.enabled) {
      return SingleChildScrollView(
        padding: bodyPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const PageTitle(title: "Rechute"),
            InstanceRelapse(
              instance: instance,
              closeEditMode: editMode.disableForm,
            ),
          ],
        ),
      );
    }

    return Container(
      color: const Color(0xFFF8FAFC),
      child: RefreshIndicator(
        color: AppTheme.primary,
        onRefresh: () async {
          context.read<InstanceDetailCubit>().getInstance();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: bodyPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Card
              _buildHeaderCard(instance),

              const SizedBox(height: 20),

              // Action buttons section
              _buildActionCard(context, editMode, instance),

              const SizedBox(height: 12),

              // Relapse section
              if (instance.relapse != null) ...[
                _buildDetailCard(
                  icon: Icons.trending_down_outlined,
                  title: "Rechute signalée le",
                  content: DateFormat.yMMMd('fr').format(
                    instance.relapse!.createdAt.toLocal(),
                  ),
                  color: AppTheme.primary,
                ),
                const SizedBox(height: 12),
              ],

              // _buildDetailCard(
              //   icon: Icons.cases_outlined,
              //   title: "Code Cas",
              //   content: instance.code,
              //   color: const Color(0xFF3B82F6),
              // ),

              // const SizedBox(height: 12),

              _buildDetailCard(
                icon: Icons.difference_outlined,
                title: "Type Cas",
                content: instance.type.getLabel(),
                color: const Color(0xFF059669),
              ),

              const SizedBox(height: 12),

              _buildDetailCard(
                icon: instance.status == InstanceStatus.OPEN
                    ? Icons.folder_open
                    : Icons.folder_off,
                title: "Statut Cas",
                content: instance.status.getLabel(),
                color: instance.status == InstanceStatus.OPEN
                    ? const Color(0xFF059669)
                    : Colors.grey[600]!,
              ),

              const SizedBox(height: 12),

              _buildDetailCard(
                icon: Icons.local_hospital_outlined,
                title: "Centre de santé",
                content: instance.healthCenter?.name ?? 'N/A',
                color: const Color(0xFF8B5CF6),
              ),

              if (instance.description != null) ...[
                const SizedBox(height: 12),
                _buildDetailCard(
                  icon: Icons.description,
                  title: "Description du cas",
                  content: instance.description!,
                  color: const Color(0xFF6B7280),
                  isLongText: true,
                ),
              ],

              const SizedBox(height: 12),

              _buildDetailCard(
                icon: Icons.calendar_today,
                title: "Créé le",
                content: DateFormat.yMMMd('fr').format(
                  instance.createdAt.toLocal(),
                ),
                color: const Color(0xFF6B7280),
              ),

              ..._listeners(context)
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderCard(InstanceShow instance) {
    final hasRelapse = instance.relapse != null;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(cardRadius),
        border: Border.all(
          color: hasRelapse
              ? AppTheme.primary.withValues(alpha: 0.2)
              : Colors.grey.withValues(alpha: 0.08),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: const Color(0xFF3B82F6).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.cases_outlined,
                  color: Color(0xFF3B82F6),
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Survivant: ${instance.survivorCode}",
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF1F2937),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      "Cas: ${instance.code}",
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF059669),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              if (hasRelapse)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    "Rechute",
                    style: TextStyle(
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.primary,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionCard(BuildContext context,
      InlineFormCubit<InstanceShow> editMode, InstanceShow instance) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.08),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Actions',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1F2937),
            ),
          ),
          const SizedBox(height: 12),
          Column(
            children: [
              // Primary actions row
              IntrinsicHeight(
                child: Row(
                  children: [
                    // Relapse button
                    Expanded(
                      child: instance.relapse != null
                          ? _buildPrimaryActionButton(
                              "Voir la rechute",
                              Icons.trending_down,
                              const Color(0xFF8B5CF6),
                              editMode.enableForm,
                              isPrimary: false,
                            )
                          : _buildPrimaryActionButton(
                              "Signaler rechute",
                              Icons.warning_amber,
                              AppTheme.primary,
                              editMode.enableForm,
                              isPrimary: true,
                            ),
                    ),
                    const SizedBox(width: 8),
                    // Status change button
                    Expanded(
                      child: instance.status == InstanceStatus.OPEN
                          ? _buildPrimaryActionButton(
                              "Décharger cas",
                              Icons.folder_off,
                              const Color(0xFF6B7280),
                              () => _editInstanceStatus(context,
                                  status: InstanceStatus.CLOSED),
                              isPrimary: false,
                            )
                          : _buildPrimaryActionButton(
                              "Relancer cas",
                              Icons.folder_open,
                              const Color(0xFF059669),
                              () => _editInstanceStatus(context,
                                  status: InstanceStatus.OPEN),
                              isPrimary: true,
                            ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 8),
              // Secondary action
              SizedBox(
                width: double.infinity,
                child: _buildSecondaryActionButton(
                  "Supprimer le cas",
                  Icons.delete_outline,
                  AppTheme.primary,
                  () => _onDelete(context),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPrimaryActionButton(
    String text,
    IconData icon,
    Color color,
    VoidCallback onPressed, {
    required bool isPrimary,
  }) {
    return SizedBox(
      height: 48, // Fixed height for consistency
      child: ElevatedButton.icon(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: isPrimary ? color : Colors.white,
          foregroundColor: isPrimary ? Colors.white : color,
          elevation: isPrimary ? 2 : 0,
          side: BorderSide(
            color: color.withValues(alpha: isPrimary ? 1.0 : 0.3),
            width: 1,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        ),
        icon: Icon(
          icon,
          size: 16,
        ),
        label: Text(
          text,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }

  Widget _buildSecondaryActionButton(
    String text,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return OutlinedButton.icon(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        foregroundColor: color,
        side: BorderSide(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
        backgroundColor: color.withValues(alpha: 0.05),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
      icon: Icon(
        icon,
        size: 18,
      ),
      label: Text(
        text,
        style: TextStyle(
          fontSize: 13,
          fontWeight: FontWeight.w600,
          color: color,
        ),
      ),
    );
  }

  Widget _buildDetailCard({
    required IconData icon,
    required String title,
    required String content,
    required Color color,
    bool isLongText = false,
  }) {
    return Container(
      padding: cardSpacing,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.08),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment:
            isLongText ? CrossAxisAlignment.start : CrossAxisAlignment.center,
        children: [
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 18,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF6B7280),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  content,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF1F2937),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _editInstanceStatus(
    BuildContext context, {
    required InstanceStatus status,
  }) async {
    final confirmed = await pressConfirm(
      context,
      content: status == InstanceStatus.OPEN
          ? "Voulez-vous relancer le cas ?"
          : "Voulez-vous vraiment decharger le cas ? ",
    );

    if (confirmed == false) return;

    // ignore: use_build_context_synchronously
    context.read<EditInstanceCubit>().editInstance(
          EditInstanceParams(
            healthCenterId: instance.healthCenterId,
            instanceId: instance.id,
            status: status,
            description: instance.description,
          ),
        );
  }

  void _onDelete(BuildContext context) async {
    final confirmed = await pressConfirm(
      context,
      content: "Voulez-vous vraiment supprimer ce cas ?",
    );

    if (confirmed) {
      // ignore: use_build_context_synchronously
      context.read<DeleteInstanceCubit>().deleteInstance(
            healthCenterId: instance.healthCenterId,
            instanceId: instance.id,
          );
    }
  }

  List<Widget> _listeners(BuildContext context) {
    return [
      // Show edit toasts message
      BlocListener<EditInstanceCubit, EditInstanceState>(
        listener: (_, state) {
          switch (state) {
            case EditInstanceLoading():
              showToast(
                context,
                type: ToastType.info,
                message: instance.status == InstanceStatus.OPEN
                    ? "Déchargement.."
                    : "Relancement..",
              );
              break;

            case EditInstanceSuccess():
              showToast(
                context,
                type: ToastType.success,
                message: instance.status == InstanceStatus.OPEN
                    ? "Déchargé avec succès"
                    : "Relancé avec succès",
              );

              context.read<InstanceDetailCubit>().getInstance();

              context
                  .read<RefreshDataBloc>()
                  .add(RefreshDataHealthCenterInstancesEvent());
              break;

            case EditInstanceError():
              showToast(
                context,
                message: state.error,
                type: ToastType.error,
              );
              break;

            default:
          }
        },
        child: const SizedBox.shrink(),
      ),

      // Show delete toasts message
      BlocListener<DeleteInstanceCubit, DeleteInstanceState>(
        listener: (_, state) {
          switch (state) {
            case DeleteInstanceLoading():
              showToast(
                context,
                type: ToastType.info,
                message: "Suppression..",
              );
              break;

            case DeleteInstanceError():
              showToast(
                context,
                type: ToastType.error,
                message: state.error,
              );
              break;

            case DeleteInstanceSuccess(message: String message):
              showToast(
                context,
                type: ToastType.success,
                message: message,
              );

              // Refresh the list after deletion
              context
                  .read<RefreshDataBloc>()
                  .add(RefreshDataHealthCenterInstancesEvent());

              // Close the dialog
              context.pop();
              break;
            default:
          }
        },
        child: const SizedBox.shrink(),
      ),
    ];
  }
}
