import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:getwidget/getwidget.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/manager/instance/instance.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/diagnostic.dart';
import 'package:s3g/src/manager/questionnaire/questionnaire.dart';

import '../blocs/delete_diagnostic/delete_diagnostic_cubit.dart';
import '../blocs/get_diagnostics/get_diagnostics_cubit.dart';
import '../widgets/diagnostic_form.dart';
import '../widgets/diagnostic_item.dart';

class DiagnosticListPage extends StatelessWidget {
  const DiagnosticListPage({super.key});

  @override
  Widget build(BuildContext context) {
    final instanceState =
        context.read<InstanceDetailCubit>().state as InstanceDetailLoaded;

    return MultiBlocProvider(
      key: ObjectKey(instanceState.instance.id),
      providers: [
        // Get Diagnostic
        BlocProvider(
          create: (_) => GetDiagnosticsCubit(
            getIt(),
            instanceId: instanceState.instance.id,
          )..getDiagnostics(),
        ),

        // Questionnaire
        BlocProvider(
          create: (_) => getIt<GetQuestionnairesCubit>()..getQuestionnaires(),
        ),

        // Delete Diagnostic
        BlocProvider(
          create: (_) => DeleteDiagnosticCubit(
            getIt(),
            instanceId: instanceState.instance.id,
          ),
        ),

        // Form
        BlocProvider(
          create: (_) => InlineFormCubit<Diagnostic>(),
        ),
      ],
      child: _DiagnosticList(instanceState.instance),
    );
  }
}

class _DiagnosticList extends StatelessWidget {
  final InstanceShow instance;
  const _DiagnosticList(this.instance);

  @override
  Widget build(BuildContext context) {
    final inlineForm = context.watch<InlineFormCubit<Diagnostic>>();

    if (inlineForm.state.enabled) {
      return SingleChildScrollView(
        physics: const ScrollPhysics(),
        padding: bodyPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const PageTitle(title: "Diagnostiques"),
            DiagnosticForm(
              editable: inlineForm.state.editable,
              instance: instance,
              closeEditMode: () {
                inlineForm.disableForm();
              },
            ),
          ],
        ),
      );
    }

    return Scaffold(
      floatingActionButton: instance.status == InstanceStatus.CLOSED
          ? const SizedBox.shrink()
          : FloatingActionButton(
              heroTag: "DiagnosticListPage",
              onPressed: () {
                inlineForm.enableForm();
              },
              child: const Icon(Icons.add),
            ),
      body: RefreshIndicator(
        onRefresh: () async {
          context.read<GetDiagnosticsCubit>().getDiagnostics();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: bodyPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const PageTitle(title: "Diagnostiques"),
              BlocBuilder<GetDiagnosticsCubit, GetDiagnosticsState>(
                builder: (_, state) {
                  switch (state) {
                    case GetDiagnosticsInitial() || GetDiagnosticsLoading():
                      return const Center(child: GFLoader());

                    case GetDiagnosticsLoaded(
                        diagnostics: List<Diagnostic> diagnostics
                      ):
                      if (diagnostics.isEmpty) {
                        return const EmptyList(
                          message: "Aucun diagnostique enregistré.",
                        );
                      }

                      return ListView.builder(
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        itemCount: diagnostics.length,
                        itemBuilder: (context, index) {
                          final diagnostic = diagnostics[index];

                          return DiagnosticItem(diagnostic: diagnostic);
                        },
                      );

                    case GetDiagnosticsError(error: String message):
                      return RetryWidget(
                          message: message,
                          onPressed: () {
                            context
                                .read<GetDiagnosticsCubit>()
                                .getDiagnostics();
                          });
                    // ignore: unreachable_switch_default
                    default:
                  }

                  return Container();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
