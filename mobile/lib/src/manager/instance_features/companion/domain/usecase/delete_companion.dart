import 'package:injectable/injectable.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/manager/instance/domain/entity/instance.dart';

import '../repository/companion_repository.dart';

@injectable
class DeleteCompanion extends UseCase<MessageResponse, DeleteCompanionParams> {
  final CompanionRepository _repository;

  DeleteCompanion(this._repository);

  @override
  call(DeleteCompanionParams params) {
    return _repository.deleteCompanion(
      instance: params.instance,
      companionId: params.companionId,
    );
  }
}

class DeleteCompanionParams {
  final Instance instance;
  final String companionId;

  DeleteCompanionParams({
    required this.instance,
    required this.companionId,
  });
}
