import 'package:injectable/injectable.dart';
import 'package:s3g/core/usecase/usecase.dart';
import 'package:s3g/src/manager/instance/domain/entity/instance.dart';

import '../entity/companion.dart';
import '../repository/companion_repository.dart';

@injectable
class GetCompanionList
    extends UseCase<List<Companion>, GetCompanionListParams> {
  final CompanionRepository _repository;

  GetCompanionList(this._repository);

  @override
  call(GetCompanionListParams params) {
    return _repository.getCompanionList(instance: params.instance);
  }
}

class GetCompanionListParams {
  final Instance instance;

  GetCompanionListParams({required this.instance});
}
