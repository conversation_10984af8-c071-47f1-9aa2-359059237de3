import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/src/manager/instance/domain/entity/instance.dart';
import 'package:s3g/src/manager/member/member.dart';

import '../entity/companion.dart';

abstract class CompanionRepository {
  RepositoryResponse<List<Companion>> getCompanionList({
    required Instance instance,
  });

  RepositoryResponse<Companion> createCompanion({
    required Instance instance,
    required MemberCompanionRole type,
    required String userId,
  });

  RepositoryResponse<MessageResponse> deleteCompanion({
    required Instance instance,
    required String companionId,
  });
}
