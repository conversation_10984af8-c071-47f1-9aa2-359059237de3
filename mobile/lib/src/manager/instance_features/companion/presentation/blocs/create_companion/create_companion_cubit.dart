import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/core/network/connection_checker.dart';
import 'package:s3g/src/manager/instance/domain/entity/instance.dart';
import 'package:s3g/src/manager/instance_features/companion/companion.dart';
import 'package:s3g/src/manager/member/member.dart';

import '../../../domain/usecase/create_companion.dart';

part 'create_companion_state.dart';

class CreateCompanionCubit extends Cubit<CreateCompanionState> {
  final CreateCompanion _createCompanion;
  final ConnectionChecker _connectionChecker;
  final Instance instance;

  CreateCompanionCubit(
    this._createCompanion,
    this._connectionChecker, {
    required this.instance,
  }) : super(CreateCompanionInitial());

  Future<void> create({
    required MemberCompanionRole type,
    required String userId,
  }) async {
    emit(CreateCompanionLoading());

    final isOnline = await _connectionChecker.isOnline();

    final result = await _createCompanion(
      CreateCompanionParams(instance: instance, type: type, userId: userId),
    );

    result.fold(
      (l) => emit(CreateCompanionError(l.message)),
      (r) => emit(CreateCompanionSuccess(r, isOffline: !isOnline)),
    );
  }
}
