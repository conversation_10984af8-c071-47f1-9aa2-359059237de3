import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/src/manager/instance/domain/entity/instance.dart';

import '../../../domain/usecase/delete_companion.dart';

part 'delete_companion_state.dart';

class DeleteCompanionCubit extends Cubit<DeleteCompanionState> {
  final DeleteCompanion _deleteCompanion;
  final Instance instance;

  DeleteCompanionCubit(
    this._deleteCompanion, {
    required this.instance,
  }) : super(DeleteCompanionInitial());

  void deleteCompanion({required String companionId}) async {
    emit(DeleteCompanionLoading());

    final result = await _deleteCompanion(
      DeleteCompanionParams(
        instance: instance,
        companionId: companionId,
      ),
    );

    result.fold(
      (failure) => emit(DeleteCompanionError(failure.message)),
      (success) => emit(DeleteCompanionSuccess(success.message)),
    );
  }
}
