part of 'create_companion_cubit.dart';

@immutable
sealed class CreateCompanionState {}

final class CreateCompanionInitial extends CreateCompanionState {}

final class CreateCompanionLoading extends CreateCompanionState {}

final class CreateCompanionSuccess extends CreateCompanionState {
  final Companion companion;
  final bool isOffline;

  CreateCompanionSuccess(this.companion, {this.isOffline = false});
}

final class CreateCompanionError extends CreateCompanionState {
  final String message;

  CreateCompanionError(this.message);
}
