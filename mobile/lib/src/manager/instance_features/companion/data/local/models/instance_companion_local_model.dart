import 'dart:convert';

import 'package:objectbox/objectbox.dart';
import 'package:s3g/src/authentication/authentication.dart';
import 'package:s3g/src/manager/instance_features/companion/companion.dart';
import 'package:s3g/src/manager/member/member.dart';
import 'package:s3g/src/sync/sync_service.dart';
import 'package:uuid/uuid.dart';

@Entity()
class InstanceCompanionLocalModel {
  @Id()
  int oid = 0;

  @Index()
  @Unique()
  String? id;

  @Index()
  @Unique()
  String localId;

  @Index()
  String instanceLocalId;

  String type;
  String userJson;

  String syncStatus;
  DateTime? lastSyncAttempt;
  String? syncError;
  bool isDeleted;

  @Property(type: PropertyType.date)
  DateTime createdAt;

  @Property(type: PropertyType.date)
  DateTime updatedAt;

  InstanceCompanionLocalModel({
    this.id,
    required this.localId,
    required this.instanceLocalId,
    required this.type,
    required this.userJson,
    required this.syncStatus,
    this.lastSyncAttempt,
    this.syncError,
    this.isDeleted = false,
    required this.createdAt,
    required this.updatedAt,
  });

  bool get needsSync =>
      SyncStatus.fromString(syncStatus) == SyncStatus.pending ||
      SyncStatus.fromString(syncStatus) == SyncStatus.failed;

  bool get isOfflineCreated => id == null;

  String get displayId => id ?? localId;

  factory InstanceCompanionLocalModel.fromEntity(
    Companion companion, {
    required String instanceLocalId,
  }) {
    final userJson = jsonEncode(UserModel.fromEntity(companion.user).toJson());

    return InstanceCompanionLocalModel(
      id: companion.id,
      localId: companion.id,
      instanceLocalId: instanceLocalId,
      type: companion.type.name,
      userJson: userJson,
      syncStatus: SyncStatus.synced.value,
      createdAt: companion.createdAt,
      updatedAt: companion.updatedAt,
    );
  }

  factory InstanceCompanionLocalModel.createOffline({
    required String instanceLocalId,
    required MemberCompanionRole type,
    required User user,
  }) {
    final now = DateTime.now();
    final userJson = jsonEncode(UserModel.fromEntity(user).toJson());

    return InstanceCompanionLocalModel(
      localId: const Uuid().v4(),
      instanceLocalId: instanceLocalId,
      type: type.name,
      userJson: userJson,
      syncStatus: SyncStatus.pending.value,
      createdAt: now,
      updatedAt: now,
    );
  }

  Companion toEntity() {
    final userMap = jsonDecode(userJson) as Map<String, dynamic>;
    final user = UserModel.fromJson(userMap);

    return Companion(
      id: displayId,
      type: MemberCompanionRole.values.firstWhere(
        (e) => e.name == type,
        orElse: () => MemberCompanionRole.values[0],
      ),
      user: user,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }

  void updateSyncStatus(SyncStatus status, {String? error}) {
    syncStatus = status.value;
    lastSyncAttempt = DateTime.now();
    syncError = error;
  }

  void markAsSynced(String serverId) {
    id = serverId;
    syncStatus = SyncStatus.synced.value;
    lastSyncAttempt = DateTime.now();
    syncError = null;
  }

  void updateContent({
    required MemberCompanionRole newType,
    required User newUser,
  }) {
    type = newType.name;
    userJson = jsonEncode(UserModel.fromEntity(newUser).toJson());
    updatedAt = DateTime.now();

    if (SyncStatus.fromString(syncStatus) == SyncStatus.synced) {
      syncStatus = SyncStatus.pending.value;
    }
  }

  void markAsDeleted() {
    isDeleted = true;
    updatedAt = DateTime.now();

    if (SyncStatus.fromString(syncStatus) == SyncStatus.synced) {
      syncStatus = SyncStatus.pending.value;
    }
  }

  CompanionModel toRemoteModel({required String instanceId}) {
    final userMap = jsonDecode(userJson) as Map<String, dynamic>;
    final user = UserModel.fromJson(userMap);

    return CompanionModel(
      id: id!,
      type: MemberCompanionRole.values.firstWhere(
        (e) => e.name == type,
        orElse: () => MemberCompanionRole.CODESA,
      ),
      user: user,
      createdAt: createdAt,
      updatedAt: updatedAt,
    );
  }
}
