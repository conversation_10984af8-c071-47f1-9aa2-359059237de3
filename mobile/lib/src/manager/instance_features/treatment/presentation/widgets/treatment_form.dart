import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:image_picker/image_picker.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/core/helpers/url.dart';
import 'package:s3g/src/manager/instance/instance.dart';
import 'package:s3g/src/manager/instance_features/treatment/treatment.dart';

import '../blocs/create_treatment/create_treatment_cubit.dart';
import '../blocs/edit_treatment/edit_treatment_cubit.dart';
import '../blocs/get_treatment/get_treatment_cubit.dart';
import '../blocs/get_treatment_list/get_treatment_list_cubit.dart';

class TreatmentForm extends StatelessWidget {
  final VoidCallback closeEditMode;
  final Treatment? editable;
  final InstanceShow instance;

  const TreatmentForm({
    super.key,
    this.editable,
    required this.closeEditMode,
    required this.instance,
  });

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      key: ValueKey(editable?.id),
      providers: [
        BlocProvider(
          create: (_) => CreateTreatmentCubit(getIt(), instanceId: instance.id),
        ),
        BlocProvider(
          create: (_) => EditTreatmentCubit(getIt(), instanceId: instance.id),
        ),
        BlocProvider(
          create: (_) => GetTreatmentCubit(
            getIt(),
            instanceId: instance.id,
          ),
        )
      ],
      child: _FormContent(
        closeEditMode: closeEditMode,
        editable: editable,
        instance: instance,
      ),
    );
  }
}

class _FormContent extends StatefulWidget {
  final VoidCallback closeEditMode;
  final Treatment? editable;
  final InstanceShow instance;

  const _FormContent({
    this.editable,
    required this.closeEditMode,
    required this.instance,
  });

  @override
  State<_FormContent> createState() => _FormContentState();
}

class _FormContentState extends State<_FormContent> {
  String? _existingAttachment;
  XFile? _fileAttachment;

  final _formKey = GlobalKey<FormBuilderState>();

  @override
  void initState() {
    if (widget.editable != null) {
      context
          .read<GetTreatmentCubit>()
          .getTreatment(treatmentId: widget.editable!.id);
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final treatmentState = context.watch<GetTreatmentCubit>().state;

    if (treatmentState is GetTreatmentSuccess) {
      _existingAttachment = treatmentState.treatment.attachment;
    }

    return FormBuilder(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.editable != null
                ? "Modifier la prise en charge:"
                : "Ajouter une prise en charge:",
            style: const TextStyle(fontSize: 18),
          ),

          // Some space
          columnSizedBox,

          // Form fields
          BlocBuilder<GetTreatmentListCubit, GetTreatmentListState>(
            builder: (_, state) {
              List<TreatmentType> types =
                  widget.editable == null ? [] : TreatmentType.values;

              if (state is GetTreatmentListLoaded && widget.editable == null) {
                types = TreatmentType.values.where((type) {
                  return state.treatments
                      .where((treatment) => treatment.type == type)
                      .isEmpty;
                }).toList();
              }

              return FormBuilderChoiceChips<TreatmentType>(
                name: "type",
                spacing: 15,
                enabled: widget.editable == null,
                selectedColor: Theme.of(context)
                    .colorScheme
                    .primary
                    .withValues(alpha: 0.8),
                initialValue: widget.editable?.type,
                validator: FormBuilderValidators.required(),
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  labelText: 'Type',
                ),
                options: types.map((type) {
                  return FormBuilderChipOption<TreatmentType>(
                    value: type,
                    child: Text(type.getLabel()),
                  );
                }).toList(),
              );
            },
          ),

          // Some space
          columnSizedBox,

          // Form fields
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // View file on edit mode
              if (widget.editable != null &&
                  _existingAttachment != null &&
                  _fileAttachment == null) ...[
                columnSizedBox,
                FileViewer(url: remoteStorageFile(_existingAttachment!)),
                columnSizedBox,
                columnSizedBox,
              ],

              // File picker
              FilePhotoPicker(onFileChange: (file) {
                setState(() {
                  _fileAttachment = file;
                });
              }),
              columnSizedBox,
            ],
          ),

          // observation
          BlocListener<GetTreatmentCubit, GetTreatmentState>(
            listener: (_, state) {
              final observation = _formKey.currentState?.fields['observation'];

              if (state is GetTreatmentSuccess && observation != null) {
                observation.didChange(state.treatment.observation);
              }
            },
            child: const SizedBox.shrink(),
          ),

          FormBuilderTextField(
            key: const ValueKey('Observation'),
            name: 'observation',
            minLines: 5,
            maxLines: 10,
            initialValue: '-----',
            keyboardType: TextInputType.multiline,
            autocorrect: true,
            enableSuggestions: true,
            textCapitalization: TextCapitalization.sentences,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              labelText: 'Observation',
            ),
          ),
          columnSizedBox,

          // Create or update buttons
          if (widget.editable != null)
            BlocBuilder<EditTreatmentCubit, EditTreatmentState>(
              builder: (_, state) {
                return FormButtons(
                  loading: state is EditTreatmentLoading,
                  onSubmit: _save,
                  onCancel: widget.closeEditMode,
                );
              },
            ),

          if (widget.editable == null)
            BlocBuilder<CreateTreatmentCubit, CreateTreatmentState>(
              builder: (_, state) {
                return FormButtons(
                  loading: state is CreateTreatmentLoading,
                  onSubmit: _save,
                  onCancel: widget.closeEditMode,
                );
              },
            ),

          // Show error message on create
          BlocListener<CreateTreatmentCubit, CreateTreatmentState>(
            listener: (_, state) {
              switch (state) {
                case CreateTreatmentSuccess():
                  widget.closeEditMode();
                  _formKey.currentState?.reset();

                  // Refresh the list
                  context.read<GetTreatmentListCubit>().getTreatmentList();
                  break;

                case CreateTreatmentFailure():
                  showToast(
                    context,
                    message: state.message,
                    type: ToastType.error,
                  );
                  break;

                default:
              }
            },
            child: const SizedBox.shrink(),
          ),

          // Show error message on edit
          BlocListener<EditTreatmentCubit, EditTreatmentState>(
            listener: (_, state) {
              switch (state) {
                case EditTreatmentSuccess():
                  widget.closeEditMode();
                  _formKey.currentState?.reset();
                  break;

                case EditTreatmentFailure():
                  showToast(
                    context,
                    message: state.message,
                    type: ToastType.error,
                  );
                  break;

                default:
              }
            },
            child: const SizedBox.shrink(),
          ),

          columnSizedBox,
          columnSizedBox,
        ],
      ),
    );
  }

  void _save() {
    final isValid = _formKey.currentState?.saveAndValidate();
    final value = _formKey.currentState?.value;

    if (isValid != null && isValid && value != null) {
      final observation = value['observation'] as String;
      final attachment = _fileAttachment?.path;

      if (widget.editable != null) {
        if ((observation.isEmpty || observation == '-----') &&
            (attachment == null && _existingAttachment == null)) {
          showToast(
            context,
            type: ToastType.error,
            message:
                "Observation et pièces jointes ne peuvent pas être vides à la fois.",
          );
          return;
        }

        context.read<EditTreatmentCubit>().editTreatment(
              treatmentId: widget.editable!.id,
              type: widget.editable!.type,
              observation: observation,
              attachment: attachment,
            );
      } else {
        if ((observation.isEmpty || observation == '-----') &&
            attachment == null) {
          showToast(
            context,
            type: ToastType.error,
            message:
                "Observation et pièces jointes ne peuvent pas être vides à la fois.",
          );
          return;
        }

        context.read<CreateTreatmentCubit>().createTreatment(
              type: value['type'] as TreatmentType,
              observation: value['observation'] as String,
              attachment: _fileAttachment?.path,
            );
      }
    }
  }
}
