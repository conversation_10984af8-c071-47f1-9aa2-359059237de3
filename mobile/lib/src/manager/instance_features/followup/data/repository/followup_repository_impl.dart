import 'package:injectable/injectable.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/paginated.dart';

import 'package:s3g/core/http/response.dart';

import 'package:s3g/core/repository/repository.dart';

import '../../domain/entity/followup.dart';
import '../../domain/repository/followup_repository.dart';
import '../remote/followup_remote_datasource.dart';

@Injectable(as: FollowupRepository)
class FollowupRepositoryImpl extends FollowupRepository {
  final FollowupRemoteDataSource _remoteDataSource;

  FollowupRepositoryImpl(this._remoteDataSource);

  @override
  RepositoryResponse<MessageResponse> deleteFollowup({
    required String instanceId,
    required String followupId,
  }) {
    return requestHelper(
      () => _remoteDataSource.deleteFollowup(
        instanceId: instanceId,
        followupId: followupId,
      ),
    );
  }

  @override
  RepositoryResponse<Followup> getFollowup({
    required String instanceId,
    required String followupId,
  }) {
    return requestHelper(
      () => _remoteDataSource.getFollowup(
        instanceId: instanceId,
        followupId: followupId,
      ),
    );
  }

  @override
  RepositoryResponse<Paginated<Followup>> getFollowupList({
    required String instanceId,
    int? page,
  }) {
    return requestHelper(
      () => _remoteDataSource.getFollowupList(
        instanceId: instanceId,
        page: page,
      ),
    );
  }
}
