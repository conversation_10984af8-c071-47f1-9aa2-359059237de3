import 'package:s3g/core/http/paginated.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/repository/repository.dart';

import '../entity/followup.dart';

abstract class FollowupRepository {
  RepositoryResponse<Followup> getFollowup({
    required String instanceId,
    required String followupId,
  });

  RepositoryResponse<Paginated<Followup>> getFollowupList({
    required String instanceId,
    int? page,
  });

  RepositoryResponse<MessageResponse> deleteFollowup({
    required String instanceId,
    required String followupId,
  });
}
