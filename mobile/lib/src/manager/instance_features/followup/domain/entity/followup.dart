import 'package:equatable/equatable.dart';
import 'package:s3g/src/manager/instance_features/companion/companion.dart';

class Followup extends Equatable {
  final String id;
  final String title;
  final Companion? companion;
  final String description;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Followup({
    required this.id,
    required this.title,
    required this.description,
    required this.companion,
    required this.createdAt,
    required this.updatedAt,
  });

  @override
  List<Object?> get props => [id];
}
