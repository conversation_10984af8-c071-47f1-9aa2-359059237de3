import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:getwidget/getwidget.dart';
import 'package:s3g/common/blocs/blocs.dart';
import 'package:s3g/common/common.dart';

typedef WidgetRender<S> = Widget Function(
  BuildContext,
  ScrollController,
  PaginatedItemState<S> state,
);

class PaginatedWidget<B extends PaginatedItemBloc<S>, S>
    extends StatefulWidget {
  final ScrollController? scrollController;

  final WidgetRender<S> render;

  final Widget? widgetEmpty;

  final Widget? loadingWidget;

  const PaginatedWidget({
    super.key,
    required this.render,
    this.widgetEmpty,
    this.scrollController,
    this.loadingWidget,
  });

  @override
  State<PaginatedWidget> createState() => _PaginatedWidgetState<B, S>();
}

class _PaginatedWidgetState<B extends PaginatedItemBloc<S>, S>
    extends State<PaginatedWidget> {
  late final ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();

    _scrollController.addListener(_onScroll);
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<B, PaginatedItemState<S>>(
      builder: (context, state) {
        switch (state.status) {
          case PaginatedItemsStatus.failure:
            if (kDebugMode) {
              print("Error : ${state.error}");
            }

            return RetryWidget(
              loading: state.loading,
              onPressed: () {
                context.read<B>().add(PaginatedItemFetched());
              },
              message: state.error,
            );

          case PaginatedItemsStatus.success:
            if (state.items.isEmpty) {
              return widget.widgetEmpty ??
                  const EmptyList(
                    message: "Aucune donnée trouvée.",
                  );
            }

            return widget.render(context, _scrollController, state);

          case PaginatedItemsStatus.initial:
            return Padding(
              padding: const EdgeInsets.only(top: 20.0),
              child: widget.loadingWidget ?? const Center(child: GFLoader()),
            );
        }
      },
    );
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);

    if (widget.scrollController == null) {
      _scrollController.dispose();
    }

    // if (widget.disposeScrollController && widget.scrollController != null) {
    //   widget.scrollController?.dispose();
    // }

    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<B>().add(PaginatedItemFetched());
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }
}
