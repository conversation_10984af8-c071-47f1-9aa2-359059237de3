import 'dart:async';

import 'package:flutter/material.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/src/sync/sync_service.dart';

class SyncStatusWidget extends StatefulWidget {
  const SyncStatusWidget({super.key});

  @override
  State<SyncStatusWidget> createState() => _SyncStatusWidgetState();
}

class _SyncStatusWidgetState extends State<SyncStatusWidget> {
  late final SyncService _syncService;

  SyncStatus _currentStatus = SyncStatus.synced;
  late StreamSubscription<SyncStatus> _subscription;

  @override
  void initState() {
    super.initState();
    _syncService = getIt<SyncService>();

    // Listen to sync status changes
    _subscription = _syncService.syncStatusStream.listen((status) {
      if (mounted) {
        setState(() {
          _currentStatus = status;
        });
      }
    });
  }

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: _buildStatusWidget(),
    );
  }

  Widget _buildStatusWidget() {
    switch (_currentStatus) {
      case SyncStatus.syncing:
        return Container(
          key: const ValueKey('syncing'),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 12,
                height: 12,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                ),
              ),
              // const SizedBox(width: 6),
              // Text(
              //   'Synchronisation...',
              //   style: TextStyle(
              //     color: Colors.blue,
              //     fontSize: 12,
              //     fontWeight: FontWeight.w500,
              //   ),
              // ),
            ],
          ),
        );

      case SyncStatus.failed:
        return Container(
          key: const ValueKey('failed'),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.red.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.sync_problem,
                size: 12,
                color: Colors.red,
              ),
              // const SizedBox(width: 6),
              // Text(
              //   'Échec de sync',
              //   style: TextStyle(
              //     color: Colors.red,
              //     fontSize: 12,
              //     fontWeight: FontWeight.w500,
              //   ),
              // ),
            ],
          ),
        );

      case SyncStatus.pending:
        return Container(
          key: const ValueKey('pending'),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.orange.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.schedule,
                size: 12,
                color: Colors.orange,
              ),
              // const SizedBox(width: 6),
              // Text(
              //   'En attente',
              //   style: TextStyle(
              //     color: Colors.orange,
              //     fontSize: 12,
              //     fontWeight: FontWeight.w500,
              //   ),
              // ),
            ],
          ),
        );

      case SyncStatus.synced:
      default:
        return Container(
          key: const ValueKey('synced'),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.green.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.check_circle,
                size: 12,
                color: Colors.green,
              ),
              // const SizedBox(width: 6),
              // Text(
              //   'Synchronisé',
              //   style: TextStyle(
              //     color: Colors.green,
              //     fontSize: 12,
              //     fontWeight: FontWeight.w500,
              //   ),
              // ),
            ],
          ),
        );
    }
  }
}

class OfflineIndicatorWidget extends StatelessWidget {
  final bool isOffline;

  const OfflineIndicatorWidget({
    super.key,
    required this.isOffline,
  });

  @override
  Widget build(BuildContext context) {
    if (!isOffline) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.cloud_off,
            size: 12,
            color: Colors.grey[600],
          ),
          const SizedBox(width: 6),
          Text(
            'Mode hors ligne',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

class SyncActionButton extends StatelessWidget {
  final VoidCallback? onPressed;

  const SyncActionButton({
    super.key,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.sync),
      onPressed: onPressed,
      tooltip: 'Synchroniser maintenant',
    );
  }
}
