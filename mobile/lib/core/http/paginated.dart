import 'package:json_annotation/json_annotation.dart';
part 'paginated.g.dart';

@JsonSerializable(genericArgumentFactories: true)
class Paginated<T> {
  @JsonKey()
  final List<T> data;

  @JsonKey()
  final PaginatedMeta meta;

  @J<PERSON><PERSON>ey()
  final PaginatedLinks links;

  Paginated({
    required this.data,
    required this.meta,
    required this.links,
  });

  factory Paginated.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$PaginatedFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) =>
      _$PaginatedToJson(this, toJsonT);

  factory Paginated.create(List<T> data) {
    return Paginated<T>(
      data: data,
      meta: PaginatedMeta(
        currentPage: 1,
        from: data.isEmpty ? 0 : 1,
        lastPage: 1,
        path: '',
        perPage: data.length,
        to: data.length,
        total: data.length,
        links: [],
      ),
      links: PaginatedLinks(
        first: '',
        last: '',
        next: null,
        prev: null,
      ),
    );
  }
}

@JsonSerializable()
class PaginatedLinks {
  @JsonKey()
  final String first;

  @JsonKey()
  final String last;

  @JsonKey()
  final String? next;

  @JsonKey()
  final String? prev;

  PaginatedLinks({
    required this.first,
    required this.last,
    required this.next,
    required this.prev,
  });

  factory PaginatedLinks.fromJson(Map<String, dynamic> json) =>
      _$PaginatedLinksFromJson(json);

  Map<String, dynamic> toJson() => _$PaginatedLinksToJson(this);
}

@JsonSerializable()
class PaginatedMeta {
  @JsonKey(name: "current_page")
  final int? currentPage;

  @JsonKey()
  final int? from;

  @JsonKey(name: "last_page")
  final int? lastPage;

  @JsonKey()
  final String path;

  @JsonKey(name: 'per_page')
  final int perPage;

  @JsonKey()
  final int? to;

  @JsonKey()
  final int total;

  @JsonKey()
  final List<PaginatedMetaLink> links;

  PaginatedMeta({
    required this.currentPage,
    required this.from,
    required this.lastPage,
    required this.path,
    required this.perPage,
    required this.to,
    required this.total,
    required this.links,
  });

  factory PaginatedMeta.fromJson(Map<String, dynamic> json) =>
      _$PaginatedMetaFromJson(json);

  Map<String, dynamic> toJson() => _$PaginatedMetaToJson(this);
}

@JsonSerializable()
class PaginatedMetaLink {
  @JsonKey()
  final bool active;

  @JsonKey()
  final String label;

  @JsonKey()
  final String? url;

  PaginatedMetaLink({
    required this.active,
    required this.label,
    required this.url,
  });

  factory PaginatedMetaLink.fromJson(Map<String, dynamic> json) =>
      _$PaginatedMetaLinkFromJson(json);

  Map<String, dynamic> toJson() => _$PaginatedMetaLinkToJson(this);
}
