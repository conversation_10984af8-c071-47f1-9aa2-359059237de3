// Global options
import 'package:dio_cache_interceptor/dio_cache_interceptor.dart';
import 'package:http_cache_file_store/http_cache_file_store.dart';
import 'package:path_provider/path_provider.dart' as pp;

Future<CacheOptions> initCacheOptions() async {
  final tmpDir = await pp.getTemporaryDirectory();
  final CacheStore cacheStore = FileCacheStore(tmpDir.path);

  return CacheOptions(
    store: cacheStore,
    policy: CachePolicy.refreshForceCache,
    hitCacheOnErrorCodes: [408, 500, 503, 504],
    maxStale: const Duration(days: 30),
    priority: CachePriority.normal,
    cipher: null,
    keyBuilder: CacheOptions.defaultCacheKeyBuilder,
    allowPostMethod: false,
    hitCacheOnNetworkFailure: true,
  );
}
