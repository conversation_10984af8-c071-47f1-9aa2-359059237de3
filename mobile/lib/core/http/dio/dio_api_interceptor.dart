import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:s3g/src/authentication/authentication.dart';

class UnauthorizedRequestInterceptor extends QueuedInterceptor {
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    log('${err.requestOptions.method} <<< ${err.requestOptions.uri}');

    if (err.response?.data is Map<String, dynamic> &&
        err.response?.data.containsKey('message')) {
      log('Error message: ${err.response?.data['message']}');
    } else {
      log('Error data: ${err.response?.data}');
    }

    handler.next(err);
  }
}

class AuthorizedRequestInterceptor extends UnauthorizedRequestInterceptor {
  AuthorizedRequestInterceptor(this._tokenRepository);

  final TokenRepository _tokenRepository;

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    try {
      log('${options.method} <<< ${options.uri}');

      options.headers[HttpHeaders.authorizationHeader] =
          await _tokenRepository.getBearerToken();

      super.onRequest(options, handler);
    } on DioException catch (e) {
      handler.reject(e);
    } on Object catch (e) {
      log(e.toString());
    }
  }
}
