import 'package:dio/dio.dart';
import 'package:s3g/core/constants/constants.dart';

final dioDefaultOptions = BaseOptions(
  baseUrl: AppConstants.apiUrl,
  connectTimeout: const Duration(seconds: 60),
  receiveTimeout: const Duration(seconds: 60),
  sendTimeout: const Duration(seconds: 60),
  headers: {
    "Accept": "application/json",
    "Content-Type": "application/json",
    'charset': 'utf-8',
  },
);
