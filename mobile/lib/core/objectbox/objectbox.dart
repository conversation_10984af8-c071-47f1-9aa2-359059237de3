import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/core/objectbox/objectbox.g.dart';

export 'objectbox.g.dart';

class ObjectBox {
  late final Store store;

  ObjectBox._create(this.store) {
    // Add any additional setup code, e.g. build queries.
  }

  static Future<ObjectBox> create() async {
    final docsDir = await getApplicationDocumentsDirectory();
    final store = await openStore(
      directory: p.join(docsDir.path, "obx-db-s3g"),
    );
    return ObjectBox._create(store);
  }

  static deleteAllFiles() async {
    getIt<ObjectBox>().store.
  }
}


