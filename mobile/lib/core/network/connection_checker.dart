import 'package:injectable/injectable.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

abstract interface class ConnectionChecker {
  Future<bool> isOnline();
}

@LazySingleton(as: ConnectionChecker)
class ConnectionCheckerImpl implements ConnectionChecker {
  // final InternetConnection _internetConnection;

  ConnectionCheckerImpl();

  @override
  Future<bool> isOnline() async => await InternetConnection().hasInternetAccess;
}
