import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

export '../config/text_input_config.dart';

class AppConstants {
  static const String appName = 'S3G';

  // API URLs
  static const String _prodApiUrl =
      'https://innovation-fund-healthcare.org/api';
  static const String _devApiUrl = 'https://s3g.smatflow.xyz/api';

  // Get API URL based on build mode
  static String get apiUrl {
    if (kDebugMode || kProfileMode) {
      return _devApiUrl;
    }

    return _prodApiUrl;
  }

  // Check if running in development mode
  static bool get isDevelopment => kDebugMode || kProfileMode;
  static bool get isProduction => kReleaseMode;
}

const bodyPadding = EdgeInsets.all(16.0);
const cardRadius = 16.0;
const cardSpacing = EdgeInsets.all(16.0);
const columnSizedBox = SizedBox(height: 16.0);
const bottomSheetRadius = ContinuousRectangleBorder(
  borderRadius: BorderRadius.only(
    topLeft: Radius.circular(50),
    topRight: Radius.circular(50),
  ),
);
