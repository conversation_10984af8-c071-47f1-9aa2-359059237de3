name: <PERSON><PERSON> and Push Docker App Image

on:
  push:
    # On commits that contains "image-build"
    branches: ["*"]

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    if: "contains(github.event.head_commit.message, 'image-build')"
    permissions:
      contents: read
      packages: write

    steps:
      - uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          cache-from: type=registry,ref=ghcr.io/${{ github.repository }}:latest
          cache-to: type=inline
          context: .
          file: .deploy/docker/Dockerfile
          push: true
          tags: |
            ghcr.io/${{ github.repository }}:latest
