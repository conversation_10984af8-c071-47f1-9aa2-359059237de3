services:
  mariadb:
    image: mariadb:10.6
    restart: always
    environment:
      MYSQL_DATABASE: "s3g"
      MYSQL_USER: "root"
      MYSQL_PASSWORD: "Uhoo7hah"
      MYSQL_ROOT_PASSWORD: "Uhoo7hah"
    ports:
      - "127.0.0.1:3306:3306"
    volumes:
      - db_data:/var/lib/mysql

  redis:
    image: "redis:alpine"
    ports:
      - "127.0.0.1:6379:6379"
    volumes:
      - redis_data:/var/lib/redis/data
    restart: always

# Names our volume
volumes:
  db_data:
  redis_data:
