FROM dunglas/frankenphp:php8.3-alpine AS base

RUN apk add --no-cache \
    libxml2-dev \
    libcurl \
    curl-dev \
    oniguruma-dev \
    freetype-dev \
    icu-dev \
    libzip-dev \
    linux-headers \
    supervisor

RUN docker-php-ext-install \
    mbstring \
    exif \
    intl \
    pcntl \
    bcmath \
    sockets \
    zip \
    gd \
    pdo_mysql

WORKDIR /app
COPY . /app

RUN mkdir -p /app/storage/app/public
RUN mkdir -p /app/bootstrap/cache
RUN mkdir -p /app/storage/framework/cache/data
RUN mkdir -p /app/storage/framework/sessions
RUN mkdir -p /app/storage/framework/views
RUN mkdir -p /app/storage/framework/testing
RUN mkdir -p /app/storage/logs

# Build frontend
FROM node:22-alpine AS frontend

RUN npm install -g npm@latest

WORKDIR /app

COPY ./package.json /app/package.json
RUN npm install

COPY . /app
RUN npm run build


# Release step
FROM base AS release

COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# # Install nodejs
# RUN apk add --no-cache nodejs
# No cache install npm
# RUN npm install -g puppeteer@^17

# Set environment variables
ENV APP_ENV=production
ENV OCTANE_SERVER=frankenphp

# Install production dependencies only
RUN composer install --no-dev --optimize-autoloader --no-interaction --no-progress

# Clear caches and optimize
RUN php artisan route:cache \
    && php artisan view:cache \
    && php artisan event:cache \
    && php artisan storage:link \
    && php artisan icons:cache \
    && php artisan filament:cache-components

# Copy assets
COPY --from=frontend /app/public /app/public

RUN mv "$PHP_INI_DIR/php.ini-production" "$PHP_INI_DIR/php.ini" && \
    sed -i 's/upload_max_filesize = 2M/upload_max_filesize = 200M/' "$PHP_INI_DIR/php.ini" && \
    sed -i 's/post_max_size = 8M/post_max_size = 200M/' "$PHP_INI_DIR/php.ini" && \
    sed -i 's/memory_limit = 128M/memory_limit = 256M/' "$PHP_INI_DIR/php.ini" && \
    sed -i 's/max_execution_time = 30/max_execution_time = 300/' "$PHP_INI_DIR/php.ini" && \
    sed -i 's/max_input_time = 60/max_input_time = 300/' "$PHP_INI_DIR/php.ini"

# Entry point script
COPY .deploy/docker/entrypoints /app/entrypoints
RUN chmod +x entrypoints/*.sh

EXPOSE 8000
