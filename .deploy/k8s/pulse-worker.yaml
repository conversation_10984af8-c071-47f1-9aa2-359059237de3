---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: pulse-worker
  namespace: s3g
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: pulse-worker
  template:
    metadata:
      labels:
        app: pulse-worker
    spec:
      containers:
        - name: pulse-worker
          image: ghcr.io/paradoxe35/s3g:latest
          imagePullPolicy: Always
          command: ["/app/entrypoints/pulse-worker.sh"]

          envFrom:
            - configMapRef:
                name: s3g-config

          resources:
            requests:
              cpu: 250m
              memory: 256Mi
            limits:
              cpu: "500m"
              memory: 512Mi

          readinessProbe:
            exec:
              command:
                - sh
                - -c
                - ps aux | grep -v grep | grep "pulse:work"
            initialDelaySeconds: 20
            periodSeconds: 10
            timeoutSeconds: 3
            successThreshold: 1
            failureThreshold: 3

          startupProbe:
            exec:
              command:
                - sh
                - -c
                - ps aux | grep -v grep | grep "pulse:work"
            initialDelaySeconds: 20
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 15
