apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: s3g

resources:
  - namespace.yaml
  - volumes.yaml
  - mariadb.yaml
  - redis.yaml
  - server.yaml
  - hostproxy.yaml
  - queue-worker.yaml
  - scheduler-worker.yaml
  - pulse-worker.yaml

configMapGenerator:
  - name: s3g-config
    envs:
      - ".env.config"

images:
  - name: ghcr.io/paradoxe35/s3g
    newTag: latest

patches:
  # Patch for PVCs
  - patch: |-
      apiVersion: v1
      kind: PersistentVolumeClaim
      metadata:
        name: not-important
      spec:
        storageClassName: nfs-client
    target:
      kind: PersistentVolumeClaim

  # JSON 6902 patch for StatefulSets with volumeClaimTemplates
  - patch: |-
      - op: replace
        path: /spec/volumeClaimTemplates/0/spec/storageClassName
        value: nfs-client
    target:
      group: apps
      version: v1
      kind: StatefulSet
    options:
      allowNameChange: true
