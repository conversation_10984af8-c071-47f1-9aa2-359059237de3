apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mariadb
  namespace: s3g
spec:
  serviceName: "s3g-mariadb"
  replicas: 1
  selector:
    matchLabels:
      app: mariadb
  template:
    metadata:
      labels:
        app: mariadb
    spec:
      containers:
        - name: mariadb
          image: mariadb:10.11
          args:
            - "--innodb-flush-method=fsync"
            - "--wait_timeout=31536000"
            - "--interactive_timeout=31536000"
            - "--max_allowed_packet=256M"
          ports:
            - containerPort: 3306
              name: mysql
          env:
            - name: MYSQL_DATABASE
              valueFrom:
                configMapKeyRef:
                  name: s3g-config
                  key: DB_DATABASE

            - name: MYSQL_USER
              valueFrom:
                configMapKeyRef:
                  name: s3g-config
                  key: DB_USERNAME

            - name: MYSQL_PASSWORD
              valueFrom:
                configMapKeyRef:
                  name: s3g-config
                  key: DB_PASSWORD

            - name: MYSQL_ROOT_PASSWORD
              valueFrom:
                configMapKeyRef:
                  name: s3g-config
                  key: DB_PASSWORD

          volumeMounts:
            - name: data
              mountPath: /var/lib/mysql
          resources:
            requests:
              cpu: "500m"
              memory: "512Mi"
            limits:
              cpu: "1"
              memory: "1Gi"
  volumeClaimTemplates:
    - metadata:
        name: data
      spec:
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: 10Gi

---
apiVersion: v1
kind: Service
metadata:
  name: s3g-mariadb
  namespace: s3g
  labels:
    app: mariadb
spec:
  ports:
    - port: 3306
      name: mysql
  clusterIP: None
  selector:
    app: mariadb
