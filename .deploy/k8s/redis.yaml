apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: s3g-redis
  namespace: s3g
spec:
  serviceName: s3g-redis
  replicas: 1
  podManagementPolicy: OrderedReady
  updateStrategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app: s3g-redis
  template:
    metadata:
      labels:
        app: s3g-redis
    spec:
      containers:
        - name: redis
          image: docker.io/redis:6.2-alpine@sha256:148bb5411c184abd288d9aaed139c98123eeb8824c5d3fce03cf721db58066d8
          ports:
            - name: redis
              containerPort: 6379
          command: ["redis-server"]
          args: ["--appendonly", "yes"]
          resources:
            requests:
              cpu: 200m
              memory: 256Mi
            limits:
              cpu: 1000m
              memory: 2Gi
          volumeMounts:
            - name: redis-data
              mountPath: /data
          livenessProbe:
            exec:
              command:
                - redis-cli
                - ping
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            exec:
              command:
                - redis-cli
                - ping
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 2
            successThreshold: 1
            failureThreshold: 3
  volumeClaimTemplates:
    - metadata:
        name: redis-data
      spec:
        accessModes: ["ReadWriteOnce"]
        storageClassName: nfs-client
        resources:
          requests:
            storage: 8Gi

---
# Redis Service (Headless service for StatefulSet)
apiVersion: v1
kind: Service
metadata:
  name: s3g-redis
  namespace: s3g
  labels:
    app: s3g-redis
spec:
  selector:
    app: s3g-redis
  ports:
    - name: redis
      port: 6379
      targetPort: 6379
  # For production StatefulSet, we use a headless service (no clusterIP)
  clusterIP: None
