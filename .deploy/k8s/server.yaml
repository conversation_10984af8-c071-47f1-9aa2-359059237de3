---
# s3g Server Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: s3g-server
  namespace: s3g
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: s3g-server
  template:
    metadata:
      labels:
        app: s3g-server
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8000"
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: app
                      operator: In
                      values:
                        - s3g-server
                topologyKey: "kubernetes.io/hostname"
      containers:
        - name: server
          image: ghcr.io/paradoxe35/s3g:latest
          imagePullPolicy: Always
          command: ["/app/entrypoints/server.sh"]
          ports:
            - name: http
              containerPort: 8000

          envFrom:
            - configMapRef:
                name: s3g-config

          volumeMounts:
            - name: upload-data
              mountPath: /app/storage/app

          resources:
            requests:
              cpu: 500m
              memory: 1Gi
            limits:
              cpu: "1000m"
              memory: 2Gi
          livenessProbe:
            httpGet:
              path: /up
              port: 8000
            initialDelaySeconds: 60
            periodSeconds: 15
            timeoutSeconds: 5
            failureThreshold: 3

          readinessProbe:
            httpGet:
              path: /up
              port: 8000
            initialDelaySeconds: 20
            periodSeconds: 10
            timeoutSeconds: 3
            successThreshold: 1
            failureThreshold: 3

          startupProbe:
            httpGet:
              path: /up
              port: 8000
            initialDelaySeconds: 20
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 15

      volumes:
        - name: upload-data
          persistentVolumeClaim:
            claimName: upload-data

---
# s3g Server Service
apiVersion: v1
kind: Service
metadata:
  name: s3g-server
  namespace: s3g
  labels:
    app: s3g-server
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "80"
spec:
  selector:
    app: s3g-server
  ports:
    - name: http
      port: 80
      targetPort: 8000
  type: NodePort
