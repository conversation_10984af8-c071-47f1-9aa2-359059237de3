<?php

namespace App\Http\Controllers\API\Instance;

use App\Http\Controllers\Controller;
use App\Http\Requests;
use App\Http\Resources\Relapse\RelapseResource;
use App\Models\Victim\Instance;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;

class RelapseController extends Controller implements HasMiddleware
{
    /**
     * Get the middleware that should be assigned to the controller.
     */
    public static function middleware(): array
    {
        return [
            new Middleware('user.belongs-to-instance-healthCenter'),
        ];
    }

    /**
     * Display the specified resource.
     */
    public function show(Instance $instance)
    {
        if (! $instance->relapse) {
            abort(403, "Aucune rechute n'a été enregistrée pour cette instance.");
        }

        return new RelapseResource($instance->relapse);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Requests\CreateRelapseRequest $request, Instance $instance)
    {
        $data = $request->validated();

        abort_if($instance->relapse, 403, __('Une rechute a déjà été enregistrée pour cette instance.'));

        $relapse = $instance->relapse()->create($data);

        return new RelapseResource($relapse);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Requests\UpdateRelapseRequest $request, Instance $instance)
    {
        $data = $request->validated();

        $relapse = $instance->relapse;

        abort_if(! $relapse, 403, __("Aucune rechute n'a été enregistrée pour cette instance."));

        $relapse->fill($data);
        $relapse->save();

        $relapse->refresh();

        return new RelapseResource($relapse);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Instance $instance)
    {
        $instance->relapse()->delete();

        return [
            'message' => __('La rechute a bien été supprimée.'),
        ];
    }
}
