<?php

namespace App\Http\Controllers\API\Companion;

use App\Http\Controllers\Controller;
use App\Http\Requests;
use App\Http\Resources\Followup\FollowupResource;
use App\Models\Victim\Companion;
use App\Models\Victim\Followup;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;

class FollowupController extends Controller implements HasMiddleware
{
    /**
     * Get the middleware that should be assigned to the controller.
     */
    public static function middleware(): array
    {
        return [
            new Middleware('user.belongs-to-companion-healthCenter'),
        ];
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Companion $companion)
    {
        $followups = $companion->followups()->latest()->paginate(30);

        return FollowupResource::collection($followups);
    }

    /**
     * Display the specified resource.
     */
    public function show(Companion $companion, Followup $followup)
    {
        $followup = $companion->followups()->where('followups.id', $followup->id)->firstOrFail();

        return new FollowupResource($followup);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Requests\CreateFollowupRequest $request, Companion $companion)
    {
        $data = $request->validated();

        $followup = $companion->followups()->create([
            ...$data,
            'instance_id' => $companion->instance->id,
        ]);

        return new FollowupResource($followup);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Requests\UpdateFollowupRequest $request, Companion $companion, Followup $followup)
    {
        $data = $request->validated();

        $followup->fill($data);
        $followup->save();

        $followup->refresh();

        return new FollowupResource($followup);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Companion $companion, Followup $followup)
    {
        $followup->deleteOrFail();

        return [
            'message' => __('Suivi supprimé avec succès'),
        ];
    }
}
