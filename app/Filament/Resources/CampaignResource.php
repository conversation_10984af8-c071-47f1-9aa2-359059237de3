<?php

namespace App\Filament\Resources;

use App\Enums;
use App\Filament\Resources\CampaignResource\Pages;
use App\Models\Campaign;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class CampaignResource extends Resource
{
    protected static ?string $model = Campaign::class;

    protected static ?string $navigationIcon = 'heroicon-o-megaphone';

    protected static ?int $navigationSort = 7;

    protected static ?string $navigationGroup = 'Global';

    protected static ?string $navigationLabel = 'Campagnes';

    protected static ?string $modelLabel = 'Campagne';

    protected static ?string $pluralModelLabel = 'Campagnes';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('health_center_id')
                    ->label(__('Centre de santé (optionnel)'))
                    ->nullable()
                    ->native(false)
                    ->relationship('healthCenter', 'name'),

                Forms\Components\Select::make('user_role')
                    ->label(__('Rôle (optionnel)'))
                    ->options(Enums\UserRole::options())
                    ->native(false)
                    ->nullable(),

                Forms\Components\TextInput::make('title')
                    ->label(__('Titre'))
                    ->required()
                    ->maxLength(255),

                Forms\Components\Textarea::make('body')
                    ->label(__('Message'))
                    ->required()
                    ->rows(5)
                    ->maxLength(2000)
                    ->columnSpanFull(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('healthCenter.name')
                    ->label(__('Centre de santé'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('user_role')
                    ->label(__('Rôle'))
                    ->badge()
                    ->sortable(),

                Tables\Columns\TextColumn::make('title')
                    ->label(__('Titre'))
                    ->searchable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label(__('Créé le'))
                    ->since()
                    ->sortable(),

            ])
            ->filters([])
            ->actions([
                Tables\Actions\ViewAction::make(),

                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ManageCampaigns::route('/'),
        ];
    }
}
